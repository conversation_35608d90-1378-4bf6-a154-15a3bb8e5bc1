# DataMind Module-System 系统功能模块指南

> 系统基础功能模块，提供用户管理、权限控制、组织架构、菜单管理等核心系统功能

## 模块概述

### 功能定位
System模块是DataMind Cloud平台的系统基础功能模块，负责：
- **用户管理**: 用户注册、登录、信息管理、状态控制
- **角色权限**: 角色定义、权限分配、数据范围控制
- **组织架构**: 部门管理、岗位管理、树形结构展示
- **菜单管理**: 系统菜单配置、按钮权限、动态菜单
- **字典管理**: 数据字典、枚举值管理、国际化支持
- **操作日志**: 用户操作记录、系统审计、安全监控

### 技术架构
- **权限框架**: Spring Security + 自定义权限注解
- **数据权限**: 基于部门的数据范围控制
- **缓存机制**: Redis缓存 + 本地缓存
- **审计日志**: AOP切面 + 异步日志记录
- **树形结构**: 递归查询 + 缓存优化

## 快速开始

### 模块依赖
```xml
<dependency>
    <groupId>com.data.platform.datamind</groupId>
    <artifactId>datamind-module-system-server</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 配置说明
```yaml
# 系统模块配置
datamind:
  system:
    # 用户配置
    user:
      init-password: admin123      # 初始密码
      password-expire-days: 90     # 密码过期天数
      max-login-attempts: 5        # 最大登录尝试次数
      lock-duration-minutes: 30    # 账户锁定时长
    
    # 权限配置
    permission:
      enable-data-scope: true      # 启用数据权限
      cache-enabled: true          # 启用权限缓存
      cache-ttl: 3600             # 缓存TTL(秒)
    
    # 操作日志配置
    operation-log:
      enabled: true               # 启用操作日志
      async: true                 # 异步记录
      retention-days: 90          # 日志保留天数
```

## 核心功能模块

### 1. 用户管理
- **用户CRUD**: 用户的创建、查询、更新、删除操作
- **密码管理**: 密码加密、重置、过期提醒
- **状态管理**: 用户启用、禁用、锁定状态控制
- **登录控制**: 登录验证、失败锁定、在线用户管理

### 2. 角色权限管理
- **角色管理**: 角色的定义、分配、继承关系
- **权限管理**: 菜单权限、按钮权限、API权限
- **数据权限**: 基于部门的数据范围控制
- **权限缓存**: 权限信息的缓存和刷新机制

### 3. 组织架构管理
- **部门管理**: 部门的层级结构、人员分配
- **岗位管理**: 岗位定义、职责描述、权限关联
- **树形展示**: 组织架构的树形展示和操作
- **数据同步**: 与外部系统的组织架构同步

### 4. 菜单管理
- **菜单配置**: 菜单的层级结构、图标、路由
- **权限控制**: 菜单的访问权限控制
- **动态菜单**: 基于权限的动态菜单生成
- **按钮权限**: 页面按钮级别的权限控制

### 5. 字典管理
- **字典类型**: 字典类型的定义和管理
- **字典数据**: 字典值的维护和排序
- **缓存机制**: 字典数据的缓存和更新
- **国际化**: 多语言字典支持

### 6. 操作日志
- **日志记录**: 用户操作的自动记录
- **日志查询**: 日志的查询、过滤、导出
- **安全审计**: 敏感操作的审计和告警
- **性能监控**: 操作性能的统计和分析

## API接口

### 用户管理API

#### 用户CRUD操作
```bash
# 获取用户列表
GET /admin-api/system/user/page?pageNo=1&pageSize=10&username=admin

# 创建用户
POST /admin-api/system/user/create
{
  "username": "testuser",
  "nickname": "测试用户",
  "password": "123456",
  "email": "<EMAIL>",
  "mobile": "13800138000",
  "sex": 1,
  "avatar": "http://example.com/avatar.jpg",
  "deptId": 1,
  "postIds": [1, 2],
  "roleIds": [2, 3],
  "remark": "测试用户账号"
}

# 获取用户详情
GET /admin-api/system/user/get?id=1

# 更新用户信息
PUT /admin-api/system/user/update
{
  "id": 1,
  "nickname": "更新昵称",
  "email": "<EMAIL>",
  "mobile": "13900139000",
  "deptId": 2,
  "postIds": [2],
  "roleIds": [2]
}

# 删除用户
DELETE /admin-api/system/user/delete?id=1
```

#### 用户状态管理
```bash
# 重置用户密码
PUT /admin-api/system/user/update-password
{
  "id": 1,
  "password": "newpassword123"
}

# 更新用户状态
PUT /admin-api/system/user/update-status
{
  "id": 1,
  "status": 0  # 0-正常 1-停用
}

# 获取用户权限信息
GET /admin-api/system/user/get-permissions?userId=1
```

### 角色管理API

#### 角色CRUD操作
```bash
# 获取角色列表
GET /admin-api/system/role/page?pageNo=1&pageSize=10

# 创建角色
POST /admin-api/system/role/create
{
  "name": "数据分析师",
  "code": "data_analyst",
  "sort": 10,
  "status": 0,
  "type": 2,
  "remark": "数据分析相关权限"
}

# 更新角色权限
PUT /admin-api/system/role/update-permission
{
  "roleId": 2,
  "menuIds": [1, 2, 3, 4, 5],
  "dataScopeType": 2,  # 数据范围类型
  "dataScopeDeptIds": [1, 2]  # 数据范围部门
}

# 分配角色给用户
POST /admin-api/system/role/assign-users
{
  "roleId": 2,
  "userIds": [1, 2, 3]
}
```

### 部门管理API

#### 部门CRUD操作
```bash
# 获取部门列表 (树形结构)
GET /admin-api/system/dept/list

# 创建部门
POST /admin-api/system/dept/create
{
  "name": "数据中心",
  "parentId": 1,
  "sort": 10,
  "leaderUserId": 2,
  "phone": "021-12345678",
  "email": "<EMAIL>",
  "status": 0
}

# 获取部门详情
GET /admin-api/system/dept/get?id=1

# 更新部门信息
PUT /admin-api/system/dept/update
{
  "id": 1,
  "name": "更新后的部门名称",
  "leaderUserId": 3,
  "sort": 20
}

# 删除部门
DELETE /admin-api/system/dept/delete?id=1
```

### 菜单管理API

#### 菜单CRUD操作
```bash
# 获取菜单列表 (树形结构)
GET /admin-api/system/menu/list

# 创建菜单
POST /admin-api/system/menu/create
{
  "name": "数据管理",
  "permission": "system:data:manage",
  "type": 1,  # 1-目录 2-菜单 3-按钮
  "sort": 10,
  "parentId": 0,
  "path": "/data",
  "icon": "data",
  "component": "Layout",
  "status": 0,
  "visible": true,
  "keepAlive": true
}

# 获取用户菜单树
GET /admin-api/system/menu/user-menu-tree?userId=1

# 更新菜单
PUT /admin-api/system/menu/update
{
  "id": 1,
  "name": "更新后的菜单名称",
  "sort": 20,
  "icon": "new-icon"
}
```

### 字典管理API

#### 字典类型管理
```bash
# 获取字典类型列表
GET /admin-api/system/dict-type/page

# 创建字典类型
POST /admin-api/system/dict-type/create
{
  "name": "用户状态",
  "type": "user_status",
  "status": 0,
  "remark": "用户状态枚举"
}

# 获取字典数据列表
GET /admin-api/system/dict-data/page?dictType=user_status

# 创建字典数据
POST /admin-api/system/dict-data/create
{
  "dictType": "user_status",
  "label": "正常",
  "value": "0",
  "sort": 1,
  "status": 0,
  "colorType": "success",
  "cssClass": "text-success"
}
```

### 操作日志API

#### 日志查询
```bash
# 获取操作日志列表
GET /admin-api/system/operate-log/page?pageNo=1&pageSize=10&module=用户管理

# 获取登录日志列表
GET /admin-api/system/login-log/page?pageNo=1&pageSize=10&userIp=***********

# 导出操作日志
GET /admin-api/system/operate-log/export?startTime=2024-01-01&endTime=2024-01-31
```

## 开发指南

### 项目结构
```
datamind-module-system/
├── datamind-module-system-api/          # API接口定义
│   ├── src/main/java/
│   │   └── com/data/platform/datamind/module/system/api/
│   │       ├── dto/                     # 数据传输对象
│   │       ├── enums/                   # 枚举定义
│   │       └── service/                 # 服务接口
├── datamind-module-system-server/       # 服务实现
│   ├── src/main/java/
│   │   └── com/data/platform/datamind/module/system/
│   │       ├── controller/
│   │       │   ├── admin/               # 管理端控制器
│   │       │   └── web/                 # Web端控制器
│   │       ├── service/
│   │       │   ├── impl/                # 服务实现类
│   │       │   ├── permission/          # 权限服务
│   │       │   └── logger/              # 日志服务
│   │       ├── dal/
│   │       │   ├── dataobject/          # 数据对象
│   │       │   └── mysql/               # MySQL映射器
│   │       └── convert/                 # 对象转换器
│   └── src/main/resources/
│       └── mapper/                      # MyBatis映射文件
└── pom.xml
```

### 权限注解使用
```java
// 控制器权限注解
@RestController
@RequestMapping("/admin-api/system/user")
@PreAuthorize("hasPermission('system:user:manage')")
public class UserController {
    
    @GetMapping("/page")
    @PreAuthorize("hasPermission('system:user:query')")
    public CommonResult<PageResult<UserRespVO>> getUserPage(@Valid UserPageReqVO pageVO) {
        // 查询用户列表
    }
    
    @PostMapping("/create")
    @PreAuthorize("hasPermission('system:user:create')")
    @OperateLog(type = OPERATE_LOG_TYPE_CREATE, module = "用户管理", name = "创建用户")
    public CommonResult<Long> createUser(@Valid @RequestBody UserCreateReqVO createReqVO) {
        // 创建用户
    }
}
```

### 数据权限使用
```java
@Service
public class UserServiceImpl implements UserService {
    
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public PageResult<UserDO> getUserPage(UserPageReqVO pageReqVO) {
        // 查询时会自动添加数据权限条件
        return userMapper.selectPage(pageReqVO);
    }
}
```

### 操作日志使用
```java
@Service
public class UserServiceImpl implements UserService {
    
    @Override
    @OperateLog(type = OPERATE_LOG_TYPE_CREATE, module = "用户管理", name = "创建用户")
    public Long createUser(UserCreateReqVO createReqVO) {
        // 方法执行前后会自动记录操作日志
        UserDO user = UserConvert.INSTANCE.convert(createReqVO);
        userMapper.insert(user);
        return user.getId();
    }
}
```

## 扩展开发

### 添加新的权限类型
```java
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {
    
    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        // 实现自定义权限验证逻辑
        return performCustomPermissionCheck(authentication, targetDomainObject, permission);
    }
}
```

### 添加新的数据权限范围
```java
@Component
public class CustomDataScopeHandler implements DataScopeHandler {
    
    @Override
    public String getDataScopeType() {
        return "CUSTOM_SCOPE";
    }
    
    @Override
    public String buildDataScopeCondition(DataScopeContext context) {
        // 构建自定义数据权限SQL条件
        return buildCustomDataScopeSQL(context);
    }
}
```

## 配置和优化

### 权限缓存配置
```yaml
datamind:
  system:
    permission:
      cache:
        enabled: true
        type: redis              # 缓存类型: redis, caffeine
        ttl: 3600               # 缓存TTL(秒)
        max-size: 10000         # 最大缓存条目数
        refresh-ahead-time: 300  # 提前刷新时间(秒)
```

### 性能优化配置
```yaml
datamind:
  system:
    # 异步处理配置
    async:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 1000
      thread-name-prefix: "system-async-"
    
    # 批量操作配置
    batch:
      size: 1000              # 批量操作大小
      timeout: 30000          # 批量操作超时时间
```

## 监控和运维

### 性能监控
```bash
# 用户在线统计
curl http://localhost:8080/actuator/metrics/system.user.online.count

# 权限验证统计
curl http://localhost:8080/actuator/metrics/system.permission.check.count

# 操作日志统计
curl http://localhost:8080/actuator/metrics/system.operate.log.count
```

### 故障排查

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 权限验证失败 | 权限配置错误或缓存问题 | 检查权限配置和清理缓存 |
| 数据权限不生效 | 数据权限配置错误 | 检查数据权限范围配置 |
| 操作日志丢失 | 异步处理异常 | 检查异步线程池配置 |
| 菜单加载慢 | 菜单树构建性能问题 | 优化菜单查询和缓存策略 |
