# DataMind Cloud 部署配置示例

## Docker Compose 开发环境配置

### docker-compose.yml
```yaml
version: '3.8'

services:
  # MySQL 主数据库
  mysql-master:
    image: mysql:8.0
    container_name: datamind-mysql-master
    environment:
      MYSQL_ROOT_PASSWORD: datamind123
      MYSQL_DATABASE: datamind_cloud
      MYSQL_USER: datamind
      MYSQL_PASSWORD: datamind123
    ports:
      - "3306:3306"
    volumes:
      - mysql_master_data:/var/lib/mysql
      - ./sql/mysql:/docker-entrypoint-initdb.d
    command: --server-id=1 --log-bin=mysql-bin --binlog-format=ROW
    networks:
      - datamind-network

  # Redis 缓存
  redis:
    image: redis:6.0-alpine
    container_name: datamind-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass datamind123
    networks:
      - datamind-network

  # Neo4j 图数据库
  neo4j:
    image: neo4j:5.0
    container_name: datamind-neo4j
    environment:
      NEO4J_AUTH: neo4j/datamind123
      NEO4J_PLUGINS: '["apoc", "graph-data-science"]'
      NEO4J_dbms_security_procedures_unrestricted: apoc.*,gds.*
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    networks:
      - datamind-network

  # Milvus 向量数据库
  milvus-etcd:
    image: quay.io/coreos/etcd:v3.5.0
    container_name: datamind-milvus-etcd
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - datamind-network

  milvus-minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: datamind-milvus-minio
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_data:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - datamind-network

  milvus-standalone:
    image: milvusdb/milvus:v2.3.0
    container_name: datamind-milvus-standalone
    command: ["milvus", "run", "standalone"]
    environment:
      ETCD_ENDPOINTS: milvus-etcd:2379
      MINIO_ADDRESS: milvus-minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "milvus-etcd"
      - "milvus-minio"
    networks:
      - datamind-network

  # Nacos 注册中心
  nacos:
    image: nacos/nacos-server:v2.3.2
    container_name: datamind-nacos
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql-master
      MYSQL_SERVICE_DB_NAME: nacos_config
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_USER: datamind
      MYSQL_SERVICE_PASSWORD: datamind123
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql-master
    volumes:
      - nacos_logs:/home/<USER>/logs
    networks:
      - datamind-network

  # API网关
  datamind-gateway:
    build:
      context: ./datamind-gateway
      dockerfile: Dockerfile
    container_name: datamind-gateway
    ports:
      - "8888:8888"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      REDIS_HOST: redis
      REDIS_PASSWORD: datamind123
    depends_on:
      - nacos
      - redis
    networks:
      - datamind-network

  # 主业务服务
  datamind-server:
    build:
      context: ./datamind-server
      dockerfile: Dockerfile
    container_name: datamind-server
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql-master
      MYSQL_DATABASE: datamind_cloud
      MYSQL_USERNAME: datamind
      MYSQL_PASSWORD: datamind123
      REDIS_HOST: redis
      REDIS_PASSWORD: datamind123
    depends_on:
      - mysql-master
      - redis
      - nacos
    networks:
      - datamind-network

volumes:
  mysql_master_data:
  redis_data:
  neo4j_data:
  neo4j_logs:
  etcd_data:
  minio_data:
  milvus_data:
  nacos_logs:

networks:
  datamind-network:
    driver: bridge
```

## Kubernetes 生产环境配置

### namespace.yaml
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: datamind-cloud
  labels:
    name: datamind-cloud
```

### configmap.yaml
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: datamind-config
  namespace: datamind-cloud
data:
  application.yml: |
    spring:
      profiles:
        active: k8s
      cloud:
        nacos:
          discovery:
            server-addr: nacos-service:8848
            namespace: datamind-cloud
          config:
            server-addr: nacos-service:8848
            namespace: datamind-cloud
            file-extension: yaml
    
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: always
    
    logging:
      level:
        com.data.platform.datamind: INFO
        org.springframework.cloud: DEBUG
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### mysql-deployment.yaml
```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql-master
  namespace: datamind-cloud
spec:
  serviceName: mysql-master-service
  replicas: 1
  selector:
    matchLabels:
      app: mysql-master
  template:
    metadata:
      labels:
        app: mysql-master
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: root-password
        - name: MYSQL_DATABASE
          value: "datamind_cloud"
        - name: MYSQL_USER
          value: "datamind"
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: user-password
        ports:
        - containerPort: 3306
          name: mysql
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
        - name: mysql-config
          mountPath: /etc/mysql/conf.d
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - mysql
            - -h
            - localhost
            - -u
            - root
            - -p${MYSQL_ROOT_PASSWORD}
            - -e
            - "SELECT 1"
          initialDelaySeconds: 5
          periodSeconds: 2
      volumes:
      - name: mysql-config
        configMap:
          name: mysql-config
  volumeClaimTemplates:
  - metadata:
      name: mysql-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-master-service
  namespace: datamind-cloud
spec:
  selector:
    app: mysql-master
  ports:
  - port: 3306
    targetPort: 3306
  clusterIP: None
```

### datamind-gateway-deployment.yaml
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: datamind-gateway
  namespace: datamind-cloud
spec:
  replicas: 3
  selector:
    matchLabels:
      app: datamind-gateway
  template:
    metadata:
      labels:
        app: datamind-gateway
    spec:
      containers:
      - name: gateway
        image: datamind/gateway:latest
        ports:
        - containerPort: 8888
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: NACOS_SERVER_ADDR
          value: "nacos-service:8848"
        - name: REDIS_HOST
          value: "redis-service"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8888
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8888
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
      volumes:
      - name: config-volume
        configMap:
          name: datamind-config

---
apiVersion: v1
kind: Service
metadata:
  name: datamind-gateway-service
  namespace: datamind-cloud
spec:
  selector:
    app: datamind-gateway
  ports:
  - port: 8888
    targetPort: 8888
  type: LoadBalancer
```

### hpa.yaml (水平自动扩缩容)
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: datamind-gateway-hpa
  namespace: datamind-cloud
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: datamind-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
```

## 监控配置示例

### prometheus-config.yaml
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: datamind-cloud
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "datamind_rules.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      - job_name: 'datamind-gateway'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - datamind-cloud
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: datamind-gateway-service
        - source_labels: [__meta_kubernetes_endpoint_port_name]
          action: keep
          regex: http
        metrics_path: /actuator/prometheus
      
      - job_name: 'datamind-server'
        kubernetes_sd_configs:
        - role: endpoints
          namespaces:
            names:
            - datamind-cloud
        relabel_configs:
        - source_labels: [__meta_kubernetes_service_name]
          action: keep
          regex: datamind-server-service
        metrics_path: /actuator/prometheus
      
      - job_name: 'mysql-exporter'
        static_configs:
        - targets: ['mysql-exporter:9104']
      
      - job_name: 'redis-exporter'
        static_configs:
        - targets: ['redis-exporter:9121']
  
  datamind_rules.yml: |
    groups:
    - name: datamind.rules
      rules:
      - alert: HighCPUUsage
        expr: cpu_usage_percent > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes"
      
      - alert: HighMemoryUsage
        expr: memory_usage_percent > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes"
      
      - alert: DatabaseConnectionPoolExhausted
        expr: hikaricp_connections_active >= hikaricp_connections_max
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection pool exhausted"
          description: "All database connections are in use"
      
      - alert: APIResponseTimeHigh
        expr: http_request_duration_seconds{quantile="0.95"} > 3
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "API response time too high"
          description: "95th percentile response time is above 3 seconds"
```

## 安全配置示例

### network-policy.yaml
```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: datamind-network-policy
  namespace: datamind-cloud
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: datamind-cloud
    - podSelector: {}
  - from: []
    ports:
    - protocol: TCP
      port: 8888  # Gateway端口
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: datamind-cloud
    - podSelector: {}
  - to: []
    ports:
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
    - protocol: TCP
      port: 443  # HTTPS
    - protocol: TCP
      port: 80   # HTTP
```

### rbac.yaml
```yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: datamind-service-account
  namespace: datamind-cloud

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: datamind-cloud
  name: datamind-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: datamind-role-binding
  namespace: datamind-cloud
subjects:
- kind: ServiceAccount
  name: datamind-service-account
  namespace: datamind-cloud
roleRef:
  kind: Role
  name: datamind-role
  apiGroup: rbac.authorization.k8s.io
```
