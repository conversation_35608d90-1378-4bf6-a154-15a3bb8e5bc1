# DataMind Server 主服务 - 产品需求文档 (PRD)

```yaml
# === PROJECT METADATA ===
project_name: "DataMind Server 主业务服务容器"
version: "v1.0"
created_date: "2025-01-04"
last_updated: "2025-01-04"
project_type: "microservice"
complexity_level: "medium"
estimated_duration: "8 weeks"
service_port: 8080
```

## 🎯 产品概览 (Product Overview)

### 核心价值主张
> DataMind Cloud平台的核心业务服务容器，提供统一的系统管理、基础设施服务和业务流程编排能力

### 目标用户画像
- **主要用户**: 系统管理员、平台运维人员、业务管理员
- **使用场景**: 
  - 用户权限管理和组织架构维护
  - 系统配置和参数管理
  - 文件存储和定时任务管理
  - 业务流程编排和服务协调
  - AI服务集成和模型管理
- **用户痛点**: 
  - 多系统用户管理分散，缺乏统一认证
  - 基础设施服务重复开发，维护成本高
  - 业务流程复杂，缺乏统一编排能力

### 成功指标
- **北极星指标**: 系统服务可用性 >99.9%
- **关键结果**: 
  - 用户认证响应时间 <100ms
  - 文件上传成功率 >99%
  - 定时任务执行准确率 >99.5%
- **验证假设**: 
  - 统一的用户管理能提升运维效率
  - 标准化的基础设施服务能降低开发成本

## 🔧 技术架构 (Technical Architecture)

### 技术栈选择
```json
{
  "framework": {
    "core": "Spring Boot 2.7.18",
    "security": "Spring Security 5.8.14",
    "data_access": "MyBatis Plus 3.5.10.1",
    "cache": "Spring Cache + Redis"
  },
  "database": {
    "primary": "MySQL 8.0",
    "cache": "Redis 6.0",
    "connection_pool": "HikariCP"
  },
  "ai_integration": {
    "framework": "Spring AI",
    "models": ["DeepSeek", "字节豆包", "腾讯混元", "讯飞星火"],
    "vector_store": ["Redis", "Qdrant", "Milvus"],
    "image_generation": "Midjourney"
  },
  "infrastructure": {
    "file_storage": "MinIO/OSS",
    "task_scheduler": "Quartz",
    "monitoring": "Spring Boot Actuator",
    "documentation": "Swagger + Knife4j"
  }
}
```

### 架构约束
- **性能要求**: API响应时间 <200ms, 支持500+并发用户
- **安全要求**: JWT认证、RBAC权限控制、操作审计日志
- **可扩展性**: 支持水平扩展，模块化设计便于功能扩展
- **可用性**: 99.9%服务可用性，支持优雅降级

## 📦 功能需求矩阵 (Feature Requirements Matrix)

### MVP版本 (P0 - 核心功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| MS001 | 用户认证与授权 | 作为管理员，我希望管理用户登录和权限，以便控制系统访问 | M | 4d | - |
| MS002 | 用户管理 | 作为管理员，我希望管理用户账号信息，以便维护用户数据 | M | 3d | MS001 |
| MS003 | 角色权限管理 | 作为管理员，我希望配置角色和权限，以便实现精细化访问控制 | M | 4d | MS001 |
| MS004 | 部门管理 | 作为管理员，我希望管理组织架构，以便支持层级化管理 | S | 2d | MS002 |
| MS005 | 菜单管理 | 作为管理员，我希望配置系统菜单，以便控制功能访问 | S | 2d | MS003 |

### 增强版本 (P1 - 重要功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| MS006 | 文件管理 | 作为用户，我希望上传和管理文件，以便存储业务数据 | M | 3d | MS001 |
| MS007 | 系统配置管理 | 作为管理员，我希望管理系统参数，以便动态调整系统行为 | S | 2d | MS001 |
| MS008 | 定时任务管理 | 作为管理员，我希望管理定时任务，以便自动化业务处理 | M | 4d | MS001 |
| MS009 | 操作日志管理 | 作为管理员，我希望查看操作日志，以便审计和问题排查 | S | 2d | MS001 |

### 完整版本 (P2 - 增值功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| MS010 | AI服务集成 | 作为开发者，我希望集成AI服务，以便为业务提供智能能力 | L | 6d | MS001 |
| MS011 | 代码生成器 | 作为开发者，我希望自动生成代码，以便提高开发效率 | M | 4d | MS001 |
| MS012 | 系统监控 | 作为运维人员，我希望监控系统状态，以便及时发现问题 | M | 3d | MS001 |

**复杂度说明**: S(Simple, 1-2天) | M(Medium, 3-5天) | L(Large, 6-10天) | XL(Extra Large, >10天)

## 📋 功能详细规格 (Detailed Specifications)

### 用户认证与授权 - MS001
```yaml
feature_id: "MS001"
feature_name: "用户认证与授权"
priority: "P0"
complexity: "M"
estimated_effort: "4d"
dependencies: []

description: |
  提供完整的用户身份认证和基于角色的权限控制系统，
  支持JWT Token认证和细粒度的权限控制。

technical_specs:
  authentication: "JWT Token + Spring Security"
  authorization: "RBAC (Role-Based Access Control)"
  session_management: "Stateless JWT"
  password_encryption: "BCrypt"
  api_endpoints:
    - method: "POST"
      path: "/admin-api/system/auth/login"
      description: "用户登录"
      request_body: |
        {
          "username": "string",
          "password": "string",
          "captcha": "string"
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "accessToken": "string",
            "refreshToken": "string",
            "expiresTime": "datetime"
          }
        }
    - method: "POST"
      path: "/admin-api/system/auth/refresh-token"
      description: "刷新Token"
    - method: "POST"
      path: "/admin-api/system/auth/logout"
      description: "用户登出"

business_logic:
  - step: "用户登录验证"
    description: "验证用户名密码和验证码"
  - step: "生成JWT Token"
    description: "生成访问令牌和刷新令牌"
  - step: "权限加载"
    description: "加载用户角色和权限信息"
  - step: "会话管理"
    description: "管理用户会话状态"

acceptance_criteria:
  - criterion: "用户能够成功登录并获得访问令牌"
    test_method: "功能测试"
  - criterion: "权限控制生效，未授权用户无法访问受保护资源"
    test_method: "安全测试"
  - criterion: "Token刷新机制正常工作"
    test_method: "集成测试"
  - criterion: "登录响应时间小于100ms"
    test_method: "性能测试"

implementation_hints:
  code_generation_prompt: |
    生成Spring Security + JWT的用户认证系统，要求：
    1. 使用Spring Boot 2.7.18和Spring Security 5.8.14
    2. 实现JWT Token生成和验证
    3. 支持RBAC权限控制
    4. 包含验证码验证
    5. 实现Token刷新机制
    6. 添加登录失败次数限制
    7. 包含完整的单元测试
  
  key_considerations:
    - "密码使用BCrypt加密存储"
    - "JWT Token设置合理的过期时间"
    - "实现防暴力破解机制"
    - "记录登录日志和异常登录"
    - "支持多端登录控制"
```

### AI服务集成 - MS010
```yaml
feature_id: "MS010"
feature_name: "AI服务集成"
priority: "P2"
complexity: "L"
estimated_effort: "6d"
dependencies: ["MS001"]

description: |
  集成多种AI大模型服务，提供统一的AI能力接口，
  支持文本生成、图像生成和向量存储等功能。

technical_specs:
  ai_framework: "Spring AI"
  supported_models:
    - "DeepSeek (深度求索)"
    - "字节豆包 (Doubao)"
    - "腾讯混元 (Hunyuan)"
    - "讯飞星火 (Spark)"
    - "智谱AI (GLM)"
    - "百川智能 (Baichuan)"
  vector_stores:
    - "Redis Vector"
    - "Qdrant"
    - "Milvus"
  image_generation: "Midjourney API"
  
  api_endpoints:
    - method: "POST"
      path: "/admin-api/ai/chat/completions"
      description: "AI对话接口"
      request_body: |
        {
          "model": "string",
          "messages": [{"role": "user", "content": "string"}],
          "temperature": "float",
          "maxTokens": "integer"
        }
    - method: "POST"
      path: "/admin-api/ai/embeddings"
      description: "文本向量化"
    - method: "POST"
      path: "/admin-api/ai/images/generate"
      description: "图像生成"

business_logic:
  - step: "模型选择"
    description: "根据请求参数选择合适的AI模型"
  - step: "请求转换"
    description: "将统一格式转换为各模型的专用格式"
  - step: "模型调用"
    description: "调用对应的AI模型API"
  - step: "响应处理"
    description: "处理模型响应并统一格式返回"
  - step: "使用统计"
    description: "记录AI服务使用情况和成本"

acceptance_criteria:
  - criterion: "支持至少5种AI模型的接入"
    test_method: "集成测试"
  - criterion: "AI服务响应时间小于5秒"
    test_method: "性能测试"
  - criterion: "向量存储和检索功能正常"
    test_method: "功能测试"
  - criterion: "图像生成功能可用"
    test_method: "功能测试"

implementation_hints:
  code_generation_prompt: |
    生成Spring AI集成系统，要求：
    1. 使用Spring AI框架
    2. 集成多种AI模型（DeepSeek、豆包等）
    3. 实现统一的AI服务接口
    4. 支持向量存储和检索
    5. 包含使用统计和成本控制
    6. 添加错误处理和重试机制
    7. 包含完整的测试用例
  
  key_considerations:
    - "AI模型的容错和降级处理"
    - "API密钥的安全管理"
    - "使用量控制和成本监控"
    - "响应缓存优化"
    - "并发请求限制"
```

## 📊 数据模型 (Data Models)

### 核心实体定义
```typescript
// 用户实体
interface AdminUser {
  id: number;
  username: string;
  password: string;
  nickname: string;
  email: string;
  mobile: string;
  deptId: number;
  postIds: number[];
  status: number; // 0-正常 1-停用
  loginIp: string;
  loginDate: Date;
  createTime: Date;
  updateTime: Date;
}

// 角色实体
interface Role {
  id: number;
  name: string;
  code: string;
  sort: number;
  status: number;
  type: number; // 1-内置角色 2-自定义角色
  remark: string;
  createTime: Date;
  updateTime: Date;
}

// 菜单实体
interface Menu {
  id: number;
  name: string;
  permission: string;
  type: number; // 1-目录 2-菜单 3-按钮
  sort: number;
  parentId: number;
  path: string;
  icon: string;
  component: string;
  status: number;
  visible: boolean;
  keepAlive: boolean;
  createTime: Date;
  updateTime: Date;
}

// 部门实体
interface Dept {
  id: number;
  name: string;
  parentId: number;
  sort: number;
  leaderUserId: number;
  phone: string;
  email: string;
  status: number;
  createTime: Date;
  updateTime: Date;
}

// 文件实体
interface FileInfo {
  id: number;
  configId: number;
  name: string;
  path: string;
  url: string;
  type: string;
  size: number;
  createTime: Date;
}
```

### API设计规范
```yaml
# RESTful API 设计标准
api_base_url: "http://localhost:8080"
authentication: "Bearer Token (JWT)"
rate_limiting: "100 requests/minute per user"

# 统一响应格式
response_format:
  success: |
    {
      "code": 0,
      "data": {...},
      "msg": "操作成功",
      "timestamp": "2025-01-04T10:00:00Z"
    }
  error: |
    {
      "code": 500,
      "data": null,
      "msg": "操作失败",
      "timestamp": "2025-01-04T10:00:00Z"
    }

# 核心API端点
api_endpoints:
  authentication:
    - "POST /admin-api/system/auth/login"
    - "POST /admin-api/system/auth/logout"
    - "POST /admin-api/system/auth/refresh-token"

  user_management:
    - "GET /admin-api/system/user/page"
    - "POST /admin-api/system/user/create"
    - "PUT /admin-api/system/user/update"
    - "DELETE /admin-api/system/user/delete"
    - "GET /admin-api/system/user/profile"

  role_management:
    - "GET /admin-api/system/role/page"
    - "POST /admin-api/system/role/create"
    - "PUT /admin-api/system/role/update"
    - "DELETE /admin-api/system/role/delete"

  menu_management:
    - "GET /admin-api/system/menu/list"
    - "POST /admin-api/system/menu/create"
    - "PUT /admin-api/system/menu/update"
    - "DELETE /admin-api/system/menu/delete"

  file_management:
    - "POST /admin-api/infra/file/upload"
    - "GET /admin-api/infra/file/page"
    - "DELETE /admin-api/infra/file/delete"
```

## 🗓️ 实施路线图 (Implementation Roadmap)

### 迭代计划
```yaml
sprint_1:
  duration: "2 weeks"
  goal: "核心认证和用户管理"
  deliverables:
    - feature_id: "MS001"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "用户认证与授权系统"
    - feature_id: "MS002"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "用户管理功能"
    - database_setup: "完成"
      assignee: "DBA团队"
      description: "数据库表结构创建"

sprint_2:
  duration: "2 weeks"
  goal: "权限管理和组织架构"
  deliverables:
    - feature_id: "MS003"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "角色权限管理"
    - feature_id: "MS004"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "部门管理"
    - feature_id: "MS005"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "菜单管理"

sprint_3:
  duration: "2 weeks"
  goal: "基础设施服务"
  deliverables:
    - feature_id: "MS006"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "文件管理服务"
    - feature_id: "MS007"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "系统配置管理"
    - feature_id: "MS008"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "定时任务管理"

sprint_4:
  duration: "2 weeks"
  goal: "增强功能和AI集成"
  deliverables:
    - feature_id: "MS009"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "操作日志管理"
    - feature_id: "MS010"
      status: "可选完成"
      assignee: "AI团队"
      description: "AI服务集成"
    - feature_id: "MS011"
      status: "可选完成"
      assignee: "后端开发团队"
      description: "代码生成器"

# 里程碑检查点
milestones:
  mvp_ready:
    date: "2025-02-15"
    criteria: "完成P0功能，系统可正常运行"
    deliverables: ["用户认证", "用户管理", "角色权限", "部门管理", "菜单管理"]

  feature_complete:
    date: "2025-03-15"
    criteria: "完成所有P1功能，基础设施服务可用"
    deliverables: ["文件管理", "系统配置", "定时任务", "操作日志"]

  production_ready:
    date: "2025-04-01"
    criteria: "完成所有功能，通过测试验收"
    deliverables: ["AI服务集成", "代码生成器", "系统监控", "性能优化"]
```

### 质量保证
- **测试覆盖率**: 单元测试 >85%, 集成测试 >75%
- **性能基准**: API响应时间 <200ms, 登录响应 <100ms
- **安全检查**: 权限控制测试、SQL注入防护、XSS防护
- **代码质量**: SonarQube质量门禁通过

## 🤖 AI协作配置 (AI Collaboration Config)

### 代码生成上下文
```yaml
project_context:
  tech_stack: "Spring Boot 2.7.18 + Spring Security + MyBatis Plus + Redis"
  coding_style: "阿里巴巴Java开发规范"
  project_structure: |
    datamind-server/
    ├── src/main/java/com/data/platform/datamind/server/
    │   ├── controller/     # 控制器层
    │   ├── service/        # 业务逻辑层
    │   ├── dal/           # 数据访问层
    │   ├── config/        # 配置类
    │   └── framework/     # 框架扩展

code_generation_templates:
  controller_prompt: |
    基于PRD功能规格生成Spring Boot Controller：
    1. 使用@RestController和@RequestMapping
    2. 实现完整的CRUD操作
    3. 添加参数验证和异常处理
    4. 包含Swagger文档注解
    5. 遵循统一响应格式
    6. 添加权限控制注解

  service_prompt: |
    基于PRD功能规格生成Spring Boot Service：
    1. 使用@Service和@Transactional
    2. 实现完整的业务逻辑
    3. 包含缓存机制
    4. 添加业务异常处理
    5. 包含详细的业务日志
```

## 💡 质量保证清单

- [x] 所有P0功能都有明确的验收标准
- [x] 技术规格可以直接用于代码生成
- [x] API设计符合RESTful规范
- [x] 数据模型定义完整
- [x] 实施计划具有可执行性
- [x] AI协作配置完整可用

## 📚 附录

### 相关文档链接
- [服务详细指南](./GUIDE.md)
- [API接口文档](http://localhost:8080/doc.html)
- [数据库设计](../sql/mysql/ruoyi-vue-pro.sql)

### 风险评估
- **技术风险**: Spring Security配置复杂，需要充分测试
- **性能风险**: 高并发下的认证性能，需要缓存优化
- **安全风险**: 权限控制的完整性，需要安全审计

---

**注意**: 本PRD文档专门针对DataMind Server主服务设计，作为整个平台的基础服务，需要确保高可用性和安全性。
