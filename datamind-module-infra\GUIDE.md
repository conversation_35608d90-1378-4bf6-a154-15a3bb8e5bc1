# DataMind Module-Infra 基础设施模块指南

> 基础设施服务模块，提供文件管理、配置管理、定时任务、代码生成等基础功能

## 模块概述

### 功能定位
Infra模块是DataMind Cloud平台的基础设施服务模块，负责：
- **文件管理**: 文件上传、下载、存储、访问控制
- **配置管理**: 系统参数配置、动态配置更新
- **定时任务**: 任务调度、执行监控、日志管理
- **代码生成**: 基于数据库表的代码自动生成
- **API文档**: 接口文档生成和管理
- **数据库管理**: 数据库文档、SQL监控

### 技术架构
- **文件存储**: 本地存储 + 云存储 (阿里云OSS、腾讯云COS等)
- **任务调度**: XXL-Job + Quartz
- **代码生成**: Velocity模板引擎 + 自定义生成器
- **配置管理**: 数据库配置 + 缓存同步
- **API文档**: Swagger + Knife4j

## 快速开始

### 模块依赖
```xml
<dependency>
    <groupId>com.data.platform.datamind</groupId>
    <artifactId>datamind-module-infra-server</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 配置说明
```yaml
# 基础设施模块配置
datamind:
  infra:
    # 文件存储配置
    file:
      storage-type: local        # 存储类型: local, aliyun-oss, tencent-cos
      local:
        base-path: ./uploads     # 本地存储路径
        domain: http://localhost:8080  # 访问域名
      aliyun-oss:
        endpoint: oss-cn-hangzhou.aliyuncs.com
        access-key-id: ${ALIYUN_ACCESS_KEY_ID}
        access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET}
        bucket-name: datamind-files
    
    # 代码生成配置
    codegen:
      base-package: com.data.platform.datamind
      author: DataMind Generator
      email: <EMAIL>
      front-type: vue3          # 前端类型: vue2, vue3, react
      
    # 定时任务配置
    job:
      enabled: true
      thread-pool-size: 10
      
    # API文档配置
    swagger:
      title: DataMind Cloud API
      description: DataMind Cloud 接口文档
      version: 1.0.0
      contact:
        name: DataMind Team
        email: <EMAIL>
```

## 核心功能模块

### 1. 文件管理
- **文件上传**: 支持单文件、多文件、大文件上传
- **存储策略**: 本地存储、云存储多种策略
- **访问控制**: 文件访问权限控制和安全验证
- **文件处理**: 图片压缩、格式转换、水印添加

### 2. 配置管理
- **参数配置**: 系统参数的配置和管理
- **动态更新**: 配置的动态更新和实时生效
- **配置分组**: 按功能模块分组管理配置
- **配置历史**: 配置变更历史和回滚功能

### 3. 定时任务
- **任务管理**: 定时任务的创建、编辑、删除
- **任务调度**: 基于Cron表达式的任务调度
- **执行监控**: 任务执行状态和结果监控
- **日志管理**: 任务执行日志和错误处理

### 4. 代码生成
- **表结构分析**: 自动分析数据库表结构
- **代码模板**: 可配置的代码生成模板
- **多层架构**: 生成Controller、Service、DAO等完整代码
- **前端代码**: 生成对应的前端页面代码

### 5. API文档管理
- **接口文档**: 自动生成API接口文档
- **在线测试**: 在线API测试功能
- **文档导出**: 支持多种格式的文档导出
- **版本管理**: API版本管理和变更记录

### 6. 数据库管理
- **数据库文档**: 自动生成数据库文档
- **SQL监控**: SQL执行监控和性能分析
- **慢查询**: 慢查询检测和优化建议
- **数据备份**: 数据库备份和恢复功能

## API接口

### 文件管理API

#### 文件上传
```bash
# 单文件上传
POST /admin-api/infra/file/upload
Content-Type: multipart/form-data
{
  "file": <file-data>,
  "path": "avatar/"  # 可选，指定存储路径
}

# 多文件上传
POST /admin-api/infra/file/upload-multiple
Content-Type: multipart/form-data
{
  "files": [<file1>, <file2>, <file3>],
  "path": "documents/"
}

# 大文件分片上传
POST /admin-api/infra/file/upload-chunk
{
  "chunkNumber": 1,
  "totalChunks": 10,
  "chunkSize": 1048576,
  "totalSize": 10485760,
  "identifier": "unique-file-id",
  "filename": "large-file.zip",
  "chunk": <chunk-data>
}
```

#### 文件管理
```bash
# 获取文件列表
GET /admin-api/infra/file/page?pageNo=1&pageSize=10&type=image

# 获取文件详情
GET /admin-api/infra/file/get?id=1

# 删除文件
DELETE /admin-api/infra/file/delete?id=1

# 批量删除文件
DELETE /admin-api/infra/file/batch-delete
{
  "ids": [1, 2, 3, 4]
}

# 文件下载
GET /admin-api/infra/file/download?id=1

# 获取文件访问URL
GET /admin-api/infra/file/get-url?id=1&expireMinutes=60
```

### 配置管理API

#### 配置CRUD操作
```bash
# 获取配置列表
GET /admin-api/infra/config/page?pageNo=1&pageSize=10&name=系统配置

# 创建配置
POST /admin-api/infra/config/create
{
  "category": "系统配置",
  "name": "系统标题",
  "key": "system.title",
  "value": "DataMind Cloud",
  "type": "STRING",
  "visible": true,
  "remark": "系统标题配置"
}

# 更新配置
PUT /admin-api/infra/config/update
{
  "id": 1,
  "value": "DataMind Cloud Platform",
  "remark": "更新系统标题"
}

# 删除配置
DELETE /admin-api/infra/config/delete?id=1

# 刷新配置缓存
POST /admin-api/infra/config/refresh-cache
```

#### 配置查询
```bash
# 根据key获取配置值
GET /admin-api/infra/config/get-value-by-key?key=system.title

# 获取配置分组
GET /admin-api/infra/config/get-by-category?category=系统配置

# 导出配置
GET /admin-api/infra/config/export?category=系统配置
```

### 定时任务API

#### 任务管理
```bash
# 获取任务列表
GET /admin-api/infra/job/page?pageNo=1&pageSize=10&name=数据同步

# 创建定时任务
POST /admin-api/infra/job/create
{
  "name": "数据同步任务",
  "status": 1,
  "handlerName": "dataSyncJobHandler",
  "handlerParam": "{\"sourceDb\":\"mysql\",\"targetDb\":\"elasticsearch\"}",
  "cronExpression": "0 0 2 * * ?",
  "retryCount": 3,
  "retryInterval": 1000,
  "monitorTimeout": 30000,
  "remark": "每日凌晨2点执行数据同步"
}

# 更新任务
PUT /admin-api/infra/job/update
{
  "id": 1,
  "cronExpression": "0 0 3 * * ?",
  "status": 1
}

# 删除任务
DELETE /admin-api/infra/job/delete?id=1
```

#### 任务执行控制
```bash
# 手动执行任务
POST /admin-api/infra/job/trigger?id=1

# 启动任务
PUT /admin-api/infra/job/update-status
{
  "id": 1,
  "status": 1  # 1-启动 2-暂停
}

# 获取任务执行日志
GET /admin-api/infra/job-log/page?jobId=1&pageNo=1&pageSize=10

# 获取任务执行详情
GET /admin-api/infra/job-log/get?id=1
```

### 代码生成API

#### 表管理
```bash
# 获取数据库表列表
GET /admin-api/infra/codegen/db/table/page?pageNo=1&pageSize=10&name=user

# 导入表
POST /admin-api/infra/codegen/create-list-from-db
{
  "dataSourceConfigId": 1,
  "tableNames": ["sys_user", "sys_role", "sys_menu"]
}

# 获取代码生成表列表
GET /admin-api/infra/codegen/table/page

# 获取表详情
GET /admin-api/infra/codegen/detail?tableId=1
```

#### 代码生成
```bash
# 预览生成代码
GET /admin-api/infra/codegen/preview?tableId=1

# 下载生成代码
GET /admin-api/infra/codegen/download?tableId=1

# 生成代码到项目
POST /admin-api/infra/codegen/sync-from-db?tableId=1

# 批量生成代码
POST /admin-api/infra/codegen/batch-download
{
  "tableIds": [1, 2, 3]
}
```

#### 生成配置
```bash
# 更新生成配置
PUT /admin-api/infra/codegen/update
{
  "table": {
    "id": 1,
    "className": "User",
    "classComment": "用户信息",
    "moduleName": "system",
    "businessName": "user",
    "functionName": "用户",
    "functionAuthor": "DataMind"
  },
  "columns": [
    {
      "id": 1,
      "javaField": "username",
      "javaType": "String",
      "columnComment": "用户名",
      "nullable": false,
      "primaryKey": false,
      "autoIncrement": false,
      "insertOperation": true,
      "updateOperation": true,
      "listOperation": true,
      "queryOperation": true,
      "queryType": "LIKE",
      "htmlType": "input"
    }
  ]
}
```

### API文档管理API

#### 文档管理
```bash
# 获取API分组列表
GET /admin-api/infra/swagger/groups

# 获取API接口列表
GET /admin-api/infra/swagger/apis?group=system

# 获取接口详情
GET /admin-api/infra/swagger/api-detail?group=system&path=/admin-api/system/user/create

# 导出API文档
GET /admin-api/infra/swagger/export?group=system&format=html
```

## 开发指南

### 项目结构
```
datamind-module-infra/
├── datamind-module-infra-api/           # API接口定义
│   ├── src/main/java/
│   │   └── com/data/platform/datamind/module/infra/api/
│   │       ├── dto/                     # 数据传输对象
│   │       ├── enums/                   # 枚举定义
│   │       └── service/                 # 服务接口
├── datamind-module-infra-server/        # 服务实现
│   ├── src/main/java/
│   │   └── com/data/platform/datamind/module/infra/
│   │       ├── controller/
│   │       │   └── admin/               # 管理端控制器
│   │       ├── service/
│   │       │   ├── file/                # 文件服务
│   │       │   ├── config/              # 配置服务
│   │       │   ├── job/                 # 定时任务服务
│   │       │   ├── codegen/             # 代码生成服务
│   │       │   └── swagger/             # API文档服务
│   │       ├── dal/
│   │       │   ├── dataobject/          # 数据对象
│   │       │   └── mysql/               # MySQL映射器
│   │       └── job/                     # 定时任务处理器
│   └── src/main/resources/
│       ├── mapper/                      # MyBatis映射文件
│       └── codegen/                     # 代码生成模板
└── pom.xml
```

### 文件存储扩展
```java
@Component
public class CustomFileStorageClient implements FileStorageClient {
    
    @Override
    public String getStorageType() {
        return "custom-storage";
    }
    
    @Override
    public String upload(byte[] content, String path, String type) throws Exception {
        // 实现自定义存储逻辑
        return performCustomUpload(content, path, type);
    }
    
    @Override
    public void delete(String path) throws Exception {
        // 实现自定义删除逻辑
        performCustomDelete(path);
    }
    
    @Override
    public byte[] getContent(String path) throws Exception {
        // 实现自定义获取逻辑
        return performCustomGet(path);
    }
}
```

### 定时任务处理器
```java
@Component
public class CustomJobHandler implements JobHandler {
    
    @Override
    public String getHandlerName() {
        return "customJobHandler";
    }
    
    @Override
    public void execute(String param) throws Exception {
        log.info("执行自定义定时任务，参数: {}", param);
        
        try {
            // 解析参数
            JSONObject paramObj = JSON.parseObject(param);
            
            // 执行业务逻辑
            performCustomJob(paramObj);
            
            log.info("自定义定时任务执行成功");
            
        } catch (Exception e) {
            log.error("自定义定时任务执行失败", e);
            throw e;
        }
    }
}
```

### 代码生成模板扩展
```java
@Component
public class CustomCodegenEngine implements CodegenEngine {
    
    @Override
    public String getTemplateName() {
        return "custom-template";
    }
    
    @Override
    public Map<String, String> execute(CodegenContext context) {
        Map<String, String> result = new HashMap<>();
        
        // 生成Controller代码
        String controllerCode = generateController(context);
        result.put("Controller.java", controllerCode);
        
        // 生成Service代码
        String serviceCode = generateService(context);
        result.put("Service.java", serviceCode);
        
        // 生成前端代码
        String vueCode = generateVueComponent(context);
        result.put("index.vue", vueCode);
        
        return result;
    }
}
```

## 配置和优化

### 文件存储优化
```yaml
datamind:
  infra:
    file:
      # 上传限制
      max-file-size: 100MB      # 单文件最大大小
      max-request-size: 500MB   # 请求最大大小
      allowed-types:            # 允许的文件类型
        - image/jpeg
        - image/png
        - application/pdf
        - text/plain
      
      # 缓存配置
      cache:
        enabled: true
        ttl: 3600              # 缓存TTL(秒)
        max-size: 1000         # 最大缓存文件数
```

### 定时任务优化
```yaml
datamind:
  infra:
    job:
      # 线程池配置
      thread-pool:
        core-size: 10
        max-size: 50
        queue-capacity: 1000
        keep-alive-seconds: 60
      
      # 监控配置
      monitor:
        enabled: true
        alert-threshold: 10000  # 告警阈值(毫秒)
        max-log-days: 30       # 日志保留天数
```

### 代码生成优化
```yaml
datamind:
  infra:
    codegen:
      # 模板配置
      template:
        base-path: classpath:codegen/
        encoding: UTF-8
        cache-enabled: true
      
      # 生成配置
      generation:
        output-path: ./generated-code/
        package-prefix: com.data.platform.datamind
        author: DataMind Generator
```

## 监控和运维

### 性能监控
```bash
# 文件上传统计
curl http://localhost:8080/actuator/metrics/infra.file.upload.count

# 定时任务执行统计
curl http://localhost:8080/actuator/metrics/infra.job.execution.count

# 代码生成统计
curl http://localhost:8080/actuator/metrics/infra.codegen.generation.count

# 配置访问统计
curl http://localhost:8080/actuator/metrics/infra.config.access.count
```

### 故障排查

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 文件上传失败 | 文件大小超限或存储空间不足 | 检查文件大小限制和存储空间 |
| 定时任务不执行 | Cron表达式错误或任务被禁用 | 检查Cron表达式和任务状态 |
| 代码生成失败 | 模板错误或数据库连接问题 | 检查模板文件和数据库配置 |
| 配置不生效 | 缓存未刷新或配置格式错误 | 刷新配置缓存或检查配置格式 |
