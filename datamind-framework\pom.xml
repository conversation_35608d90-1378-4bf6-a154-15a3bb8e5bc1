<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>datamind</artifactId>
        <groupId>com.data.platform</groupId>
        <version>${revision}</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>datamind-common</module>
        <module>datamind-spring-boot-starter-env</module>
        <module>datamind-spring-boot-starter-mybatis</module>
        <module>datamind-spring-boot-starter-redis</module>
        <module>datamind-spring-boot-starter-web</module>
        <module>datamind-spring-boot-starter-security</module>
        <module>datamind-spring-boot-starter-websocket</module>

        <module>datamind-spring-boot-starter-monitor</module>
        <module>datamind-spring-boot-starter-protection</module>
<!--        <module>datamind-spring-boot-starter-config</module>-->
        <module>datamind-spring-boot-starter-job</module>
        <module>datamind-spring-boot-starter-mq</module>
        <module>datamind-spring-boot-starter-rpc</module>

        <module>datamind-spring-boot-starter-excel</module>

        <module>datamind-spring-boot-starter-biz-tenant</module>
        <module>datamind-spring-boot-starter-biz-data-permission</module>
        <module>datamind-spring-boot-starter-biz-ip</module>
    </modules>

    <artifactId>datamind-framework</artifactId>
    <description>
        该包是技术组件，每个子包，代表一个组件。每个组件包括两部分：
            1. core 包：是该组件的核心封装
            2. config 包：是该组件基于 Spring 的配置

        技术组件，也分成两类：
            1. 框架组件：和我们熟悉的 MyBatis、Redis 等等的拓展
            2. 业务组件：和业务相关的组件的封装，例如说数据字典、操作日志等等。
        如果是业务组件，Maven 名字会包含 biz
    </description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

</project>
