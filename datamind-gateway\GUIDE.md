# DataMind Gateway API网关服务指南

> 基于Spring Cloud Gateway的统一API网关，提供路由转发、安全认证、限流熔断等功能

## 服务概述

### 功能定位
DataMind Gateway是整个DataMind Cloud平台的统一入口，负责：
- **统一路由**: 所有外部请求的统一入口和路由转发
- **安全认证**: JWT Token验证和用户身份认证
- **权限控制**: 基于角色的API访问权限控制
- **限流熔断**: 接口限流、熔断保护和降级处理
- **监控统计**: API调用统计、性能监控和日志记录

### 技术架构
- **网关框架**: Spring Cloud Gateway 3.4.1
- **服务发现**: Nacos 2.3.2
- **安全框架**: Spring Security + JWT
- **限流组件**: Redis + Sentinel
- **监控组件**: Micrometer + Prometheus

## 快速开始

### 环境要求
- **Java**: JDK 8+
- **Nacos**: 2.3.2+ (服务注册中心)
- **Redis**: 6.0+ (限流存储)

### 本地启动
```bash
# 1. 进入网关目录
cd datamind-gateway

# 2. 配置Nacos和Redis连接
vim src/main/resources/application-local.yaml

# 3. 启动网关服务
mvn spring-boot:run -Dspring.profiles.active=local

# 4. 验证网关状态
curl http://localhost:8888/actuator/health
```

### 核心配置
```yaml
# 服务配置
server:
  port: 8888

# Nacos配置
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: datamind-cloud
        group: DEFAULT_GROUP
    
    # Gateway路由配置
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 主服务路由
        - id: datamind-server
          uri: lb://datamind-server
          predicates:
            - Path=/admin-api/system/**,/admin-api/infra/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
        
        # 数据元数据服务路由
        - id: datamind-server-data-meta
          uri: lb://datamind-server-data-meta
          predicates:
            - Path=/admin-api/data-meta/**,/web-api/data-meta/**
          filters:
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 50
                redis-rate-limiter.burstCapacity: 100

# Redis配置 (限流存储)
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 3000ms

# JWT配置
datamind:
  security:
    jwt:
      secret: datamind-cloud-jwt-secret-key-2024
      expire-time: 7200  # 2小时
      refresh-expire-time: 604800  # 7天
    
    # 白名单配置 (无需认证的路径)
    permit-all-urls:
      - /admin-api/system/auth/login
      - /admin-api/system/auth/refresh-token
      - /actuator/**
      - /doc.html
      - /swagger-resources/**
      - /v3/api-docs/**
```

## 核心功能模块

### 1. 路由管理
- **动态路由**: 基于Nacos的动态服务发现和路由配置
- **负载均衡**: 支持多种负载均衡策略
- **路径重写**: 灵活的路径重写和转发规则
- **协议转换**: HTTP/HTTPS协议转换支持

### 2. 安全认证
- **JWT认证**: 基于JWT Token的无状态认证
- **Token刷新**: 自动Token刷新机制
- **权限验证**: 基于角色和权限的API访问控制
- **IP白名单**: 支持IP白名单访问控制

### 3. 限流熔断
- **接口限流**: 基于Redis的分布式限流
- **熔断保护**: 服务熔断和自动恢复
- **降级处理**: 服务降级和备用响应
- **超时控制**: 请求超时控制和处理

### 4. 监控统计
- **访问统计**: API调用次数、响应时间统计
- **错误监控**: 错误率监控和告警
- **性能监控**: 网关性能指标监控
- **日志记录**: 详细的访问日志和审计日志

## 路由配置

### 服务路由配置
```yaml
spring:
  cloud:
    gateway:
      routes:
        # 主服务路由
        - id: datamind-server
          uri: lb://datamind-server
          predicates:
            - Path=/admin-api/system/**,/admin-api/infra/**
          filters:
            - name: AuthenticationFilter
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 100
                redis-rate-limiter.burstCapacity: 200
                redis-rate-limiter.requestedTokens: 1
        
        # 数据元数据服务路由
        - id: datamind-server-data-meta
          uri: lb://datamind-server-data-meta
          predicates:
            - Path=/admin-api/data-meta/**,/web-api/data-meta/**
          filters:
            - name: AuthenticationFilter
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 50
                redis-rate-limiter.burstCapacity: 100
        
        # 规则引擎服务路由
        - id: datamind-server-rule-engine
          uri: lb://datamind-server-rule-engine
          predicates:
            - Path=/admin-api/rule-engine/**
          filters:
            - name: AuthenticationFilter
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 30
                redis-rate-limiter.burstCapacity: 60
        
        # 数据语义服务路由
        - id: datamind-server-data-semantic
          uri: lb://datamind-server-data-semantic
          predicates:
            - Path=/web-api/data-semantic/**
          filters:
            - name: AuthenticationFilter
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 40
                redis-rate-limiter.burstCapacity: 80
        
        # 数据巡查服务路由
        - id: datamind-server-data-inspection
          uri: lb://datamind-server-data-inspection
          predicates:
            - Path=/admin-api/data-inspection/**,/web-api/data-inspection/**
          filters:
            - name: AuthenticationFilter
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 40
        
        # 营销稽查服务路由
        - id: datamind-server-emss-inspection
          uri: lb://datamind-server-emss-inspection
          predicates:
            - Path=/admin-api/emss-inspection/**
          filters:
            - name: AuthenticationFilter
            - name: RequestRateLimiter
              args:
                redis-rate-limiter.replenishRate: 20
                redis-rate-limiter.burstCapacity: 40
```

### 全局过滤器配置
```yaml
spring:
  cloud:
    gateway:
      # 全局过滤器
      default-filters:
        - name: GlobalLoggingFilter
        - name: GlobalExceptionFilter
        - name: CorsFilter
        - name: RequestTimeFilter
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: "*"
            allowed-methods: "*"
            allowed-headers: "*"
            allow-credentials: true
            max-age: 3600
```

## 安全配置

### JWT认证配置
```java
@Configuration
@EnableWebFluxSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityWebFilterChain securityWebFilterChain(ServerHttpSecurity http) {
        return http
            .csrf().disable()
            .cors().and()
            .authorizeExchange(exchanges -> exchanges
                // 白名单路径
                .pathMatchers("/admin-api/system/auth/**").permitAll()
                .pathMatchers("/actuator/**").permitAll()
                .pathMatchers("/doc.html", "/swagger-resources/**", "/v3/api-docs/**").permitAll()
                // 管理端API需要认证
                .pathMatchers("/admin-api/**").authenticated()
                // Web端API需要认证
                .pathMatchers("/web-api/**").authenticated()
                .anyExchange().authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt.jwtDecoder(jwtDecoder()))
            )
            .build();
    }
}
```

### 自定义认证过滤器
```java
@Component
public class AuthenticationFilter implements GlobalFilter, Ordered {
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getURI().getPath();
        
        // 检查是否为白名单路径
        if (isPermitAllUrl(path)) {
            return chain.filter(exchange);
        }
        
        // 获取Authorization头
        String authHeader = request.getHeaders().getFirst("Authorization");
        if (StringUtils.isEmpty(authHeader) || !authHeader.startsWith("Bearer ")) {
            return handleUnauthorized(exchange);
        }
        
        // 验证JWT Token
        String token = authHeader.substring(7);
        try {
            Claims claims = jwtUtil.parseToken(token);
            
            // 将用户信息添加到请求头
            ServerHttpRequest mutatedRequest = request.mutate()
                .header("X-User-Id", claims.getSubject())
                .header("X-User-Name", claims.get("username", String.class))
                .header("X-User-Roles", claims.get("roles", String.class))
                .build();
            
            return chain.filter(exchange.mutate().request(mutatedRequest).build());
            
        } catch (Exception e) {
            log.warn("JWT Token验证失败: {}", e.getMessage());
            return handleUnauthorized(exchange);
        }
    }
}
```

## 限流配置

### Redis限流配置
```java
@Configuration
public class RateLimiterConfig {
    
    @Bean
    public RedisRateLimiter redisRateLimiter() {
        return new RedisRateLimiter(
            10,  // replenishRate: 每秒允许的请求数
            20,  // burstCapacity: 令牌桶容量
            1    // requestedTokens: 每次请求消耗的令牌数
        );
    }
    
    @Bean
    public KeyResolver userKeyResolver() {
        return exchange -> {
            // 基于用户ID限流
            String userId = exchange.getRequest().getHeaders().getFirst("X-User-Id");
            return Mono.just(userId != null ? userId : "anonymous");
        };
    }
    
    @Bean
    public KeyResolver ipKeyResolver() {
        return exchange -> {
            // 基于IP限流
            String ip = getClientIP(exchange.getRequest());
            return Mono.just(ip);
        };
    }
}
```

### 熔断配置
```yaml
resilience4j:
  circuitbreaker:
    instances:
      datamind-server:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
        minimum-number-of-calls: 5
      datamind-server-rule-engine:
        failure-rate-threshold: 60
        wait-duration-in-open-state: 60s
        sliding-window-size: 20
```

## 监控配置

### Actuator监控
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
```

### 自定义监控指标
```java
@Component
public class GatewayMetrics {
    
    private final Counter requestCounter;
    private final Timer requestTimer;
    private final Gauge activeConnections;
    
    public GatewayMetrics(MeterRegistry meterRegistry) {
        this.requestCounter = Counter.builder("gateway.requests.total")
            .description("Total number of requests")
            .tag("gateway", "datamind")
            .register(meterRegistry);
            
        this.requestTimer = Timer.builder("gateway.requests.duration")
            .description("Request duration")
            .register(meterRegistry);
            
        this.activeConnections = Gauge.builder("gateway.connections.active")
            .description("Active connections")
            .register(meterRegistry, this, GatewayMetrics::getActiveConnections);
    }
}
```

## 开发指南

### 项目结构
```
datamind-gateway/
├── src/main/java/
│   └── com/data/platform/datamind/gateway/
│       ├── DatamindGatewayApplication.java     # 启动类
│       ├── config/
│       │   ├── SecurityConfig.java             # 安全配置
│       │   ├── RateLimiterConfig.java          # 限流配置
│       │   └── CorsConfig.java                 # 跨域配置
│       ├── filter/
│       │   ├── AuthenticationFilter.java       # 认证过滤器
│       │   ├── LoggingFilter.java              # 日志过滤器
│       │   └── ExceptionFilter.java            # 异常处理过滤器
│       ├── util/
│       │   ├── JwtUtil.java                    # JWT工具类
│       │   └── ResponseUtil.java               # 响应工具类
│       └── handler/
│           └── GlobalExceptionHandler.java     # 全局异常处理
├── src/main/resources/
│   ├── application.yaml                        # 主配置文件
│   ├── application-local.yaml                 # 本地环境配置
│   └── bootstrap.yaml                         # 启动配置
└── pom.xml
```

### 自定义过滤器开发
```java
@Component
public class CustomGatewayFilter implements GlobalFilter, Ordered {
    
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        
        // 请求前处理
        log.info("请求路径: {}, 方法: {}", request.getURI().getPath(), request.getMethod());
        
        return chain.filter(exchange).then(Mono.fromRunnable(() -> {
            // 响应后处理
            log.info("响应状态: {}", response.getStatusCode());
        }));
    }
    
    @Override
    public int getOrder() {
        return -1; // 过滤器执行顺序
    }
}
```

## 部署配置

### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY target/datamind-gateway.jar app.jar
EXPOSE 8888
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 环境变量
```bash
# Nacos配置
NACOS_SERVER_ADDR=localhost:8848
NACOS_NAMESPACE=datamind-cloud

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRE_TIME=7200
```

## 监控和运维

### 健康检查
```bash
# 网关健康状态
curl http://localhost:8888/actuator/health

# 路由信息
curl http://localhost:8888/actuator/gateway/routes

# 过滤器信息
curl http://localhost:8888/actuator/gateway/globalfilters
```

### 性能监控
```bash
# 请求统计
curl http://localhost:8888/actuator/metrics/gateway.requests.total

# 响应时间
curl http://localhost:8888/actuator/metrics/gateway.requests.duration

# 限流统计
curl http://localhost:8888/actuator/metrics/spring.cloud.gateway.requests
```

### 故障排查

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 路由不通 | 服务未注册或路由配置错误 | 检查Nacos注册和路由配置 |
| 认证失败 | JWT配置错误或Token过期 | 检查JWT配置和Token有效性 |
| 限流触发 | 请求频率超过限制 | 调整限流参数或优化客户端 |
| 服务超时 | 后端服务响应慢 | 调整超时配置或优化后端服务 |
