# DataMind Cloud 数据智能平台 - 产品需求文档 (PRD)

```yaml
# === PROJECT METADATA ===
project_name: "DataMind Cloud 数据智能平台"
version: "v1.0"
created_date: "2025-01-04"
last_updated: "2025-01-04"
project_type: "web_app"
complexity_level: "complex"
estimated_duration: "24 weeks"
```

## 🎯 产品概览 (Product Overview)

### 核心价值主张
> 基于AI驱动的企业级数据治理与智能分析平台，通过自然语言查询实现数据民主化，让业务人员无需SQL技能即可获得数据洞察

### 目标用户画像
- **主要用户**: 企业数据分析师、业务人员、数据工程师、IT管理员
- **使用场景**: 
  - 自然语言数据查询与分析
  - 数据元数据管理与治理
  - 营销稽查智能体应用
  - 数据质量监控与巡查
  - 企业数据资产管理
- **用户痛点**: 
  - SQL技能门槛高，业务人员难以直接查询数据
  - 数据分散，缺乏统一的元数据管理
  - 数据质量问题难以及时发现和处理
  - 营销合规检查依赖人工，效率低下

### 成功指标
- **北极星指标**: 平台日活跃用户数 (DAU)
- **关键结果**: 
  - NL2SQL查询准确率 >85%
  - 数据查询响应时间 <3秒
  - 用户满意度评分 >4.5/5.0
- **验证假设**: 
  - 自然语言查询能显著降低数据分析门槛
  - AI驱动的数据治理能提升数据质量
  - 智能稽查能提高合规检查效率

## 🔧 技术架构 (Technical Architecture)

### 技术栈选择
```json
{
  "frontend": {
    "framework": "Vue3",
    "language": "TypeScript",
    "styling": "Element Plus",
    "build_tool": "Vite",
    "alternatives": ["Vue3 + Vben(Ant Design)", "Vue2 + Element UI", "UniApp"]
  },
  "backend": {
    "framework": "Spring Boot 2.7.18",
    "language": "Java 8+",
    "microservice": "Spring Cloud Alibaba 2021.0.6.2",
    "security": "Spring Security 5.8.14",
    "data_access": "MyBatis Plus ********"
  },
  "database": {
    "primary": "MySQL 8.0",
    "cache": "Redis 6.0",
    "vector": "Milvus/Qdrant",
    "graph": "Neo4j",
    "alternatives": ["PostgreSQL", "Oracle", "SQL Server"]
  },
  "ai_services": {
    "models": ["DeepSeek", "字节豆包", "腾讯混元", "讯飞星火", "智谱AI"],
    "rag_engine": "RAGFlow",
    "vector_store": "Redis/Qdrant/Milvus",
    "nl2sql": "Dify + AI Models"
  },
  "infrastructure": {
    "gateway": "Spring Cloud Gateway 3.4.1",
    "registry": "Nacos 2.3.2",
    "scheduler": "XXL-Job 2.4.0",
    "monitoring": "SkyWalking + Spring Boot Admin",
    "containerization": "Docker + Docker Compose"
  }
}
```

### 架构约束
- **性能要求**: API响应时间 <500ms, NL2SQL查询 <3s, 支持1000+并发用户
- **安全要求**: JWT认证、RBAC权限控制、数据脱敏、审计日志
- **可扩展性**: 微服务架构支持水平扩展，支持多租户SaaS模式
- **可用性**: 99.9%服务可用性，支持故障自动恢复

## 📦 功能需求矩阵 (Feature Requirements Matrix)

### MVP版本 (P0 - 核心功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| F001 | 用户认证与权限管理 | 作为管理员，我希望管理用户账号和权限，以便控制系统访问 | M | 5d | - |
| F002 | 数据源连接管理 | 作为数据工程师，我希望配置多种数据源连接，以便统一管理数据 | M | 4d | F001 |
| F003 | 元数据采集与管理 | 作为数据工程师，我希望自动采集数据库元数据，以便构建数据目录 | L | 8d | F002 |
| F004 | NL2SQL智能查询 | 作为业务人员，我希望用自然语言查询数据，以便快速获得分析结果 | XL | 15d | F003 |
| F005 | 语义原子管理 | 作为数据分析师，我希望定义语义原子，以便标准化业务指标 | L | 7d | F003 |

### 增强版本 (P1 - 重要功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| F006 | 数据质量监控 | 作为数据工程师，我希望监控数据质量，以便及时发现数据问题 | L | 6d | F003 |
| F007 | 营销稽查智能体 | 作为合规人员，我希望自动化稽查营销活动，以便提高合规效率 | XL | 12d | F004,F005 |
| F008 | 知识图谱构建 | 作为数据科学家，我希望构建数据知识图谱，以便增强语义理解 | L | 10d | F005 |
| F009 | RAG检索增强 | 作为系统，我希望通过RAG提升查询准确性，以便更好地理解用户意图 | L | 8d | F004,F008 |

### 完整版本 (P2 - 增值功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| F010 | 数据血缘分析 | 作为数据治理专员，我希望追踪数据血缘关系，以便理解数据流向 | L | 8d | F003,F008 |
| F011 | 智能报表生成 | 作为业务人员，我希望自动生成数据报表，以便定期获得业务洞察 | M | 6d | F004 |
| F012 | 移动端应用 | 作为移动用户，我希望在手机上查询数据，以便随时获得数据支持 | M | 8d | F004 |

**复杂度说明**: S(Simple, 1-2天) | M(Medium, 3-5天) | L(Large, 6-10天) | XL(Extra Large, >10天)

## 📋 功能详细规格 (Detailed Specifications)

### 用户认证与权限管理 - F001
```yaml
feature_id: "F001"
feature_name: "用户认证与权限管理"
priority: "P0"
complexity: "M"
estimated_effort: "5d"
dependencies: []

description: |
  提供完整的用户身份认证和基于角色的权限控制系统，支持多租户架构，
  确保系统安全性和数据隔离。

technical_specs:
  authentication: "JWT Token + Spring Security"
  authorization: "RBAC (Role-Based Access Control)"
  multi_tenant: "基于租户ID的数据隔离"
  api_endpoints:
    - method: "POST"
      path: "/admin-api/system/auth/login"
      description: "用户登录"
      request_body: |
        {
          "username": "string",
          "password": "string",
          "captcha": "string"
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "accessToken": "string",
            "refreshToken": "string",
            "expiresTime": "datetime"
          }
        }
    - method: "GET"
      path: "/admin-api/system/user/profile"
      description: "获取用户信息"
      response_body: |
        {
          "code": 0,
          "data": {
            "id": "long",
            "username": "string",
            "nickname": "string",
            "roles": ["string"],
            "permissions": ["string"]
          }
        }

business_logic:
  - step: "用户登录验证"
    description: "验证用户名密码，生成JWT Token"
  - step: "权限检查"
    description: "基于用户角色检查API访问权限"
  - step: "会话管理"
    description: "管理用户会话状态和Token刷新"

acceptance_criteria:
  - criterion: "用户能够成功登录并获得访问令牌"
    test_method: "集成测试"
  - criterion: "权限控制生效，未授权用户无法访问受保护资源"
    test_method: "安全测试"
  - criterion: "支持多租户数据隔离"
    test_method: "数据隔离测试"

implementation_hints:
  code_generation_prompt: |
    生成Spring Security + JWT的用户认证系统，要求：
    1. 使用Spring Boot 2.7.18和Spring Security 5.8.14
    2. 实现JWT Token生成和验证
    3. 支持RBAC权限控制
    4. 包含用户管理CRUD操作
    5. 添加多租户支持
    6. 包含完整的单元测试和集成测试
  
  key_considerations:
    - "密码使用BCrypt加密存储"
    - "JWT Token设置合理的过期时间"
    - "实现Token刷新机制"
    - "添加登录失败次数限制"
    - "记录用户操作审计日志"
```

### NL2SQL智能查询 - F004
```yaml
feature_id: "F004"
feature_name: "NL2SQL智能查询"
priority: "P0"
complexity: "XL"
estimated_effort: "15d"
dependencies: ["F003"]

description: |
  核心AI功能，将用户的自然语言查询转换为SQL语句并执行，
  支持多种AI模型和RAG检索增强，提供智能的数据查询体验。

technical_specs:
  ai_models: ["DeepSeek", "字节豆包", "腾讯混元", "讯飞星火"]
  workflow_engine: "Dify工作流"
  rag_enhancement: "RAGFlow + 向量检索"
  vector_storage: "Redis/Qdrant/Milvus"
  api_endpoints:
    - method: "POST"
      path: "/api/v1/query/nl2sql"
      description: "自然语言转SQL查询"
      request_body: |
        {
          "query": "string",
          "dataSourceId": "long",
          "context": "string",
          "options": {
            "model": "string",
            "temperature": "float"
          }
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "sql": "string",
            "explanation": "string",
            "results": [{}],
            "executionTime": "long",
            "confidence": "float"
          }
        }

business_logic:
  - step: "自然语言理解"
    description: "使用AI模型理解用户查询意图"
  - step: "语义检索"
    description: "通过RAG检索相关的元数据和语义信息"
  - step: "SQL生成"
    description: "基于元数据和语义信息生成SQL语句"
  - step: "SQL执行"
    description: "在目标数据源执行SQL并返回结果"
  - step: "结果解释"
    description: "生成查询结果的自然语言解释"

acceptance_criteria:
  - criterion: "NL2SQL转换准确率达到85%以上"
    test_method: "准确率测试"
  - criterion: "查询响应时间小于3秒"
    test_method: "性能测试"
  - criterion: "支持复杂查询（多表关联、聚合函数等）"
    test_method: "功能测试"

implementation_hints:
  code_generation_prompt: |
    生成NL2SQL智能查询系统，要求：
    1. 集成多种AI模型（DeepSeek、豆包等）
    2. 实现Dify工作流调用
    3. 集成RAGFlow检索增强
    4. 支持多种向量数据库
    5. 包含SQL安全检查和执行限制
    6. 添加查询缓存机制
    7. 包含完整的测试用例

  key_considerations:
    - "SQL注入防护和安全检查"
    - "查询结果缓存优化"
    - "支持查询历史记录"
    - "AI模型调用的容错处理"
    - "向量检索的性能优化"
```

## 📊 数据模型 (Data Models)

### 核心实体定义
```typescript
// 用户实体
interface AdminUser {
  id: number;
  username: string;
  password: string;
  nickname: string;
  email: string;
  mobile: string;
  deptId: number;
  roleIds: number[];
  status: number;
  tenantId: number;
  createTime: Date;
  updateTime: Date;
}

// 数据源实体
interface DataSource {
  id: number;
  name: string;
  type: string; // MYSQL, POSTGRESQL, ORACLE等
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  status: number;
  tenantId: number;
  createTime: Date;
  updateTime: Date;
}

// 元数据表实体
interface MetaTable {
  id: number;
  dbId: number;
  tableName: string;
  tableComment: string;
  tableOwner: string;
  tableRows: number;
  dataLength: number;
  engine: string;
  status: number;
  tenantId: number;
  createTime: Date;
  updateTime: Date;
}

// 元数据字段实体
interface MetaColumn {
  id: number;
  tableId: number;
  columnName: string;
  columnComment: string;
  dataType: string;
  columnLength: number;
  nullable: boolean;
  defaultValue: string;
  isPrimaryKey: boolean;
  isIndex: boolean;
  tenantId: number;
  createTime: Date;
  updateTime: Date;
}

// 语义原子实体
interface SemanticAtom {
  id: number;
  atomName: string;
  atomCode: string;
  atomType: string; // DIMENSION, MEASURE, FILTER
  description: string;
  businessDomain: string;
  dataType: string;
  expression: string;
  sqlTemplate: string;
  status: string;
  version: string;
  tags: string[]; // JSON格式
  metadata: object; // JSON格式
  tenantId: number;
  createTime: Date;
  updateTime: Date;
}

// NL2SQL查询记录
interface QueryRecord {
  id: number;
  userId: number;
  naturalQuery: string;
  generatedSql: string;
  dataSourceId: number;
  executionTime: number;
  resultCount: number;
  confidence: number;
  status: string;
  errorMessage: string;
  tenantId: number;
  createTime: Date;
}
```

### 关系映射
```mermaid
erDiagram
    AdminUser ||--o{ QueryRecord : creates
    AdminUser }o--|| Dept : belongs_to
    AdminUser }o--o{ Role : has

    DataSource ||--o{ MetaTable : contains
    MetaTable ||--o{ MetaColumn : has
    MetaTable }o--o{ SemanticAtom : maps_to

    QueryRecord }o--|| DataSource : queries
    QueryRecord }o--o{ SemanticAtom : uses

    SemanticAtom }o--o{ SemanticAtom : relates_to
```

### API设计规范
```yaml
# RESTful API 设计标准
api_base_url: "http://localhost:8888/api/v1"
authentication: "Bearer Token (JWT)"
rate_limiting: "100 requests/minute per user"
api_versioning: "URL路径版本控制 (/api/v1/)"

# 统一响应格式
response_format:
  success: |
    {
      "code": 0,
      "data": {...},
      "msg": "success",
      "timestamp": "2025-01-04T10:00:00Z"
    }
  error: |
    {
      "code": 500,
      "data": null,
      "msg": "Error description",
      "timestamp": "2025-01-04T10:00:00Z"
    }

# 核心API端点
api_endpoints:
  authentication:
    - "POST /admin-api/system/auth/login"
    - "POST /admin-api/system/auth/logout"
    - "POST /admin-api/system/auth/refresh-token"

  data_source:
    - "GET /api/v1/datasource/list"
    - "POST /api/v1/datasource/create"
    - "PUT /api/v1/datasource/{id}"
    - "DELETE /api/v1/datasource/{id}"
    - "POST /api/v1/datasource/{id}/test-connection"

  metadata:
    - "GET /api/v1/meta/databases"
    - "GET /api/v1/meta/tables"
    - "GET /api/v1/meta/columns"
    - "POST /api/v1/meta/sync"

  nl2sql:
    - "POST /api/v1/query/nl2sql"
    - "GET /api/v1/query/history"
    - "POST /api/v1/query/explain"

  semantic:
    - "GET /api/v1/semantic/atoms"
    - "POST /api/v1/semantic/atoms"
    - "PUT /api/v1/semantic/atoms/{id}"
    - "DELETE /api/v1/semantic/atoms/{id}"
```

## 🗓️ 实施路线图 (Implementation Roadmap)

### 迭代计划
```yaml
sprint_1:
  duration: "3 weeks"
  goal: "基础架构和用户管理"
  deliverables:
    - feature_id: "F001"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "用户认证与权限管理系统"
    - infrastructure_setup: "完成"
      assignee: "DevOps团队"
      description: "基础设施搭建（数据库、Redis、Nacos等）"
    - frontend_framework: "完成"
      assignee: "前端开发团队"
      description: "前端框架搭建和基础组件开发"

sprint_2:
  duration: "3 weeks"
  goal: "数据源和元数据管理"
  deliverables:
    - feature_id: "F002"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "数据源连接管理"
    - feature_id: "F003"
      status: "必须完成"
      assignee: "数据团队"
      description: "元数据采集与管理"
    - api_gateway: "完成"
      assignee: "后端开发团队"
      description: "API网关配置和路由规则"

sprint_3:
  duration: "4 weeks"
  goal: "语义管理和AI集成"
  deliverables:
    - feature_id: "F005"
      status: "必须完成"
      assignee: "数据团队"
      description: "语义原子管理系统"
    - ai_integration: "完成"
      assignee: "AI团队"
      description: "AI模型集成和Dify工作流配置"
    - vector_storage: "完成"
      assignee: "数据团队"
      description: "向量数据库配置和RAG检索"

sprint_4:
  duration: "4 weeks"
  goal: "NL2SQL核心功能"
  deliverables:
    - feature_id: "F004"
      status: "必须完成"
      assignee: "AI团队+后端团队"
      description: "NL2SQL智能查询系统"
    - frontend_integration: "完成"
      assignee: "前端开发团队"
      description: "前端查询界面和结果展示"
    - testing: "完成"
      assignee: "测试团队"
      description: "系统集成测试和性能测试"

sprint_5:
  duration: "3 weeks"
  goal: "增强功能和优化"
  deliverables:
    - feature_id: "F006"
      status: "必须完成"
      assignee: "数据团队"
      description: "数据质量监控"
    - feature_id: "F009"
      status: "必须完成"
      assignee: "AI团队"
      description: "RAG检索增强"
    - performance_optimization: "完成"
      assignee: "全体团队"
      description: "性能优化和bug修复"

sprint_6:
  duration: "3 weeks"
  goal: "营销稽查智能体"
  deliverables:
    - feature_id: "F007"
      status: "必须完成"
      assignee: "AI团队+业务团队"
      description: "营销稽查智能体"
    - feature_id: "F008"
      status: "可选完成"
      assignee: "数据团队"
      description: "知识图谱构建"
    - user_acceptance_testing: "完成"
      assignee: "测试团队+业务团队"
      description: "用户验收测试"

# 里程碑检查点
milestones:
  mvp_demo:
    date: "2025-03-15"
    criteria: "完成F001-F005核心功能，可进行基础的NL2SQL查询"
    deliverables: ["用户管理", "数据源管理", "元数据管理", "NL2SQL查询", "语义原子管理"]

  beta_release:
    date: "2025-05-01"
    criteria: "完成所有P0和P1功能，系统稳定性达到生产要求"
    deliverables: ["数据质量监控", "营销稽查智能体", "RAG检索增强", "性能优化"]

  production_ready:
    date: "2025-06-15"
    criteria: "完成所有功能开发，通过安全审计和性能测试"
    deliverables: ["知识图谱", "智能报表", "移动端应用", "部署文档"]
```

### 质量保证
- **测试覆盖率**: 单元测试 >80%, 集成测试 >70%, E2E测试 >60%
- **性能基准**: API响应时间 <500ms, NL2SQL查询 <3s, 页面加载 <2s
- **安全检查**: 代码安全扫描、依赖漏洞检查、渗透测试
- **代码质量**: SonarQube代码质量检查，代码审查流程

## 🤖 AI协作配置 (AI Collaboration Config)

### 代码生成上下文
```yaml
# 项目上下文文件
project_context:
  tech_stack:
    backend: "Spring Boot 2.7.18 + Spring Cloud Alibaba + MyBatis Plus"
    frontend: "Vue3 + TypeScript + Element Plus + Vite"
    database: "MySQL 8.0 + Redis 6.0 + Neo4j + Milvus"
    ai_services: "Spring AI + Dify + RAGFlow + 多AI模型"

  coding_style: |
    - 遵循阿里巴巴Java开发规范
    - 使用RESTful API设计原则
    - 采用分层架构：Controller -> Service -> DAO
    - 统一异常处理和响应格式
    - 完善的日志记录和监控

  project_structure: |
    datamind-cloud-mini-service/
    ├── datamind-server/                    # 主服务
    ├── datamind-server-data-meta/          # 元数据服务
    ├── datamind-server-data-semantic/      # 语义服务
    ├── datamind-server-rule-engine/        # 规则引擎服务
    ├── datamind-server-emss-inspection/    # 营销稽查服务
    ├── datamind-server-data-inspection/    # 数据巡查服务
    ├── datamind-gateway/                   # API网关
    ├── datamind-module-system/             # 系统模块
    ├── datamind-module-infra/              # 基础设施模块
    └── datamind-ui/                        # 前端项目

# AI提示词模板
code_generation_templates:
  controller_prompt: |
    基于PRD中的功能规格[功能ID]，生成Spring Boot Controller代码：
    1. 使用@RestController和@RequestMapping注解
    2. 实现完整的CRUD操作API
    3. 包含参数验证(@Valid)和异常处理
    4. 添加Swagger文档注解(@Operation, @Parameter)
    5. 遵循统一的响应格式CommonResult<T>
    6. 包含权限控制注解(@PreAuthorize)
    7. 添加操作日志记录

  service_prompt: |
    基于PRD中的功能规格[功能ID]，生成Spring Boot Service代码：
    1. 使用@Service注解和@Transactional事务管理
    2. 实现完整的业务逻辑
    3. 包含参数校验和业务异常处理
    4. 集成缓存机制(@Cacheable, @CacheEvict)
    5. 添加详细的业务日志
    6. 包含单元测试用例

  frontend_prompt: |
    基于PRD中的功能规格[功能ID]，生成Vue3 + TypeScript前端代码：
    1. 使用Composition API和<script setup>语法
    2. 集成Element Plus组件库
    3. 实现响应式设计和数据绑定
    4. 包含表单验证和错误处理
    5. 添加Loading状态和用户反馈
    6. 遵循TypeScript类型安全
    7. 包含组件测试用例

# 数据库设计模板
database_design_template: |
  基于PRD中的数据模型，生成MySQL数据库设计：
  1. 遵循数据库设计规范和命名约定
  2. 包含完整的表结构、索引和约束
  3. 添加适当的注释和文档
  4. 考虑性能优化和分区策略
  5. 包含初始化数据和测试数据
  6. 支持多租户数据隔离

# API文档生成模板
api_doc_template: |
  基于PRD中的API规格，生成Swagger/OpenAPI文档：
  1. 完整的API端点定义和参数说明
  2. 请求/响应示例和数据模型
  3. 错误码定义和处理说明
  4. 认证和权限要求
  5. 接口调用示例和测试用例
```

### 上下文传递规则
- **功能开发**: 每次AI交互都包含相关的功能规格(F001-F012)和技术约束
- **架构一致性**: 保持微服务架构和技术栈选择的一致性
- **数据模型**: 传递完整的实体关系和数据库设计约束
- **API设计**: 遵循RESTful设计原则和统一响应格式
- **测试标准**: 包含单元测试、集成测试和E2E测试要求
- **安全要求**: 传递认证、授权和数据安全相关约束

### 迭代更新指令
```yaml
feature_change_handling: |
  当功能需求发生变更时：
  1. 更新对应的功能规格(F001-F012)
  2. 评估对依赖功能的影响
  3. 更新相关的API设计和数据模型
  4. 调整实施计划和里程碑
  5. 通知相关开发团队

requirement_addition: |
  当添加新需求时：
  1. 分析新需求的优先级和复杂度
  2. 分配新的功能ID(F013+)
  3. 评估对现有架构的影响
  4. 更新技术栈和依赖关系
  5. 调整开发计划和资源分配

priority_adjustment: |
  当调整功能优先级时：
  1. 重新评估P0/P1/P2功能分类
  2. 调整Sprint计划和里程碑
  3. 重新分配开发资源
  4. 更新测试计划和验收标准
  5. 同步项目干系人
```

## 💡 质量保证清单

生成PRD后，确保包含：
- [x] 所有P0功能都有明确的验收标准
- [x] 技术规格可以直接用于代码生成
- [x] API设计符合RESTful规范
- [x] 数据模型定义完整
- [x] 实施计划具有可执行性
- [x] AI协作配置完整可用
- [x] 文档结构清晰、格式规范

## 📚 附录

### 相关文档链接
- [技术架构详细设计](./GUIDE.md)
- [数据库设计文档](./sql/mysql/)
- [API接口文档](http://localhost:8888/doc.html)
- [部署运维指南](./script/)
- [开发环境搭建](./README.md)

### 术语表
- **NL2SQL**: Natural Language to SQL，自然语言转SQL查询
- **RAG**: Retrieval-Augmented Generation，检索增强生成
- **元数据**: 描述数据的数据，包括表结构、字段信息等
- **语义原子**: 业务语义的最小单元，用于标准化业务指标
- **向量数据库**: 专门存储和检索向量数据的数据库
- **知识图谱**: 以图的形式表示知识的语义网络

### 风险评估
- **技术风险**: AI模型准确率不达标，需要持续优化和训练
- **性能风险**: 大规模数据查询可能影响响应时间，需要缓存和优化
- **安全风险**: 数据访问权限控制，需要完善的安全机制
- **集成风险**: 多个AI服务集成复杂度高，需要容错处理

---

**注意**: 本PRD文档是活文档，将根据项目进展和需求变化持续更新。所有技术规格都经过详细设计，可直接指导AI工具进行代码生成和开发工作。
