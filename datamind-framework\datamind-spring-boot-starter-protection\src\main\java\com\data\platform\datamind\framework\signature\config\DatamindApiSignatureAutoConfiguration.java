package com.data.platform.datamind.framework.signature.config;

import com.data.platform.datamind.framework.redis.config.DatamindRedisAutoConfiguration;
import com.data.platform.datamind.framework.signature.core.aop.ApiSignatureAspect;
import com.data.platform.datamind.framework.signature.core.redis.ApiSignatureRedisDAO;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * HTTP API 签名的自动配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration(after = DatamindRedisAutoConfiguration.class)
public class DatamindApiSignatureAutoConfiguration {

    @Bean
    public ApiSignatureAspect signatureAspect(ApiSignatureRedisDAO signatureRedisDAO) {
        return new ApiSignatureAspect(signatureRedisDAO);
    }

    @Bean
    public ApiSignatureRedisDAO signatureRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new ApiSignatureRedisDAO(stringRedisTemplate);
    }

}
