kind: pipeline
type: docker
name: build-and-push

steps:
  - name: build-and-push-image
    image: plugins/kaniko
    pull: if-not-exists
    environment:
      HTTP_PROXY: http://clash.base.askying.top:7890
      HTTPS_PROXY: http://clash.base.askying.top:7890
      NO_PROXY: localhost,127.0.0.1,***********/24
    settings:
      registry: harbor.base.askying.top:33443
      repo: harbor.base.askying.top:33443/datamind/datamind-gateway #
      tags: latest #
      context: ./ #
      username: #harbor
        from_secret: ci_username
      password: #harbor
        from_secret: ci_password
      insecure: false
      cache: false

