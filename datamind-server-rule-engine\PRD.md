# DataMind Rule-Engine 规则引擎服务 - 产品需求文档 (PRD)

```yaml
# === PROJECT METADATA ===
project_name: "DataMind Rule-Engine AI智能查询核心引擎"
version: "v1.0"
created_date: "2025-01-04"
last_updated: "2025-01-04"
project_type: "microservice"
complexity_level: "complex"
estimated_duration: "10 weeks"
service_port: 8082
```

## 🎯 产品概览 (Product Overview)

### 核心价值主张
> AI驱动的智能查询核心引擎，通过NL2SQL技术让业务人员用自然语言查询数据，降低数据分析门槛，实现数据民主化

### 目标用户画像
- **主要用户**: 业务分析师、数据分析师、业务人员、产品经理
- **使用场景**: 
  - 自然语言数据查询和分析
  - 复杂业务问题的SQL生成
  - 数据探索和即席查询
  - 报表和仪表板数据获取
  - 业务指标计算和分析
- **用户痛点**: 
  - SQL技能门槛高，业务人员难以直接查询数据
  - 复杂查询编写困难，容易出错
  - 数据查询效率低，依赖技术人员支持
  - 业务语义与技术实现存在鸿沟

### 成功指标
- **北极星指标**: NL2SQL查询准确率 >85%
- **关键结果**: 
  - 查询响应时间 <3秒
  - 用户查询成功率 >90%
  - 日均查询量 >1000次
- **验证假设**: 
  - 自然语言查询能显著降低数据分析门槛
  - AI增强的查询能提升查询准确性和效率

## 🔧 技术架构 (Technical Architecture)

### 技术栈选择
```json
{
  "ai_integration": {
    "workflow_engine": "Dify",
    "rag_engine": "RAGFlow",
    "ai_models": ["DeepSeek", "字节豆包", "腾讯混元", "讯飞星火", "智谱AI"],
    "vector_store": ["Redis", "Qdrant", "Milvus"]
  },
  "framework": {
    "core": "Spring Boot 2.7.18",
    "ai_framework": "Spring AI",
    "data_access": "MyBatis Plus 3.5.10.1",
    "cache": "Redis 6.0"
  },
  "processing_modes": {
    "two_stage": "NL2Semantic + Semantic2SQL",
    "direct": "Direct NL2SQL",
    "rag_enhanced": "RAG-Enhanced Query Processing"
  },
  "optimization": {
    "sql_validation": "SQL语法和语义验证",
    "sql_optimization": "查询性能优化",
    "caching": "智能查询缓存",
    "monitoring": "实时性能监控"
  }
}
```

### 架构约束
- **性能要求**: NL2SQL转换 <3秒, SQL执行 <5秒, 支持100+并发查询
- **准确性要求**: 简单查询准确率 >95%, 复杂查询准确率 >80%
- **安全要求**: SQL注入防护、查询权限控制、敏感数据保护
- **可扩展性**: 支持新AI模型接入、支持多种数据源类型

## 📦 功能需求矩阵 (Feature Requirements Matrix)

### MVP版本 (P0 - 核心功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| RE001 | 基础NL2SQL转换 | 作为业务人员，我希望用自然语言查询数据，以便快速获得分析结果 | XL | 8d | - |
| RE002 | SQL验证与优化 | 作为系统，我希望验证生成的SQL，以便确保查询安全和性能 | M | 3d | RE001 |
| RE003 | 查询结果执行 | 作为用户，我希望执行生成的SQL，以便获得查询结果 | M | 3d | RE002 |
| RE004 | 查询历史管理 | 作为用户，我希望查看查询历史，以便重复使用和优化查询 | S | 2d | RE003 |
| RE005 | AI模型集成 | 作为系统，我希望集成多种AI模型，以便提供稳定的AI服务 | L | 5d | - |

### 增强版本 (P1 - 重要功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| RE006 | 两阶段处理 | 作为系统，我希望支持NL2Semantic+Semantic2SQL，以便提高复杂查询准确性 | XL | 10d | RE001,RE005 |
| RE007 | RAG检索增强 | 作为系统，我希望通过RAG技术增强查询理解，以便提高查询准确性 | L | 6d | RE006 |
| RE008 | Dify工作流集成 | 作为系统，我希望集成Dify工作流，以便支持复杂的业务处理逻辑 | L | 5d | RE005 |
| RE009 | 智能查询缓存 | 作为系统，我希望缓存查询结果，以便提高响应速度 | M | 3d | RE003 |

### 完整版本 (P2 - 增值功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| RE010 | 批量查询处理 | 作为用户，我希望批量处理多个查询，以便提高工作效率 | M | 4d | RE003 |
| RE011 | 查询解释说明 | 作为用户，我希望了解SQL的含义，以便学习和验证查询逻辑 | M | 3d | RE002 |
| RE012 | 性能监控分析 | 作为运维人员，我希望监控查询性能，以便优化系统表现 | M | 3d | RE003 |

**复杂度说明**: S(Simple, 1-2天) | M(Medium, 3-5天) | L(Large, 6-10天) | XL(Extra Large, >10天)

## 📋 功能详细规格 (Detailed Specifications)

### 基础NL2SQL转换 - RE001
```yaml
feature_id: "RE001"
feature_name: "基础NL2SQL转换"
priority: "P0"
complexity: "XL"
estimated_effort: "8d"
dependencies: []

description: |
  核心的自然语言到SQL转换功能，支持多种AI模型，
  能够理解用户的自然语言查询意图并生成对应的SQL语句。

technical_specs:
  ai_models: ["DeepSeek", "字节豆包", "腾讯混元", "讯飞星火"]
  input_processing: "自然语言预处理和意图识别"
  context_enhancement: "数据库schema和业务上下文注入"
  sql_generation: "基于模板和规则的SQL生成"
  
  api_endpoints:
    - method: "POST"
      path: "/api/v1/rule-engine/nl2sql/query"
      description: "自然语言转SQL查询"
      request_body: |
        {
          "query": "string",
          "dataSourceId": "long",
          "model": "string",
          "options": {
            "temperature": "float",
            "maxTokens": "integer",
            "includeExplanation": "boolean"
          }
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "sql": "string",
            "explanation": "string",
            "confidence": "float",
            "executionPlan": "string",
            "estimatedRows": "integer"
          }
        }
    - method: "POST"
      path: "/api/v1/rule-engine/nl2sql/validate"
      description: "验证生成的SQL"
    - method: "POST"
      path: "/api/v1/rule-engine/nl2sql/optimize"
      description: "优化SQL查询"

business_logic:
  - step: "自然语言预处理"
    description: "清洗和标准化用户输入的自然语言查询"
  - step: "意图识别"
    description: "识别查询类型（查询、统计、分析等）"
  - step: "上下文构建"
    description: "获取相关的数据库schema和业务上下文"
  - step: "AI模型调用"
    description: "调用选定的AI模型进行SQL生成"
  - step: "SQL后处理"
    description: "对生成的SQL进行格式化和基础验证"
  - step: "置信度评估"
    description: "评估生成SQL的准确性和可执行性"

acceptance_criteria:
  - criterion: "简单查询（单表查询）准确率达到95%以上"
    test_method: "准确率测试"
  - criterion: "复杂查询（多表关联）准确率达到80%以上"
    test_method: "准确率测试"
  - criterion: "查询转换响应时间小于3秒"
    test_method: "性能测试"
  - criterion: "生成的SQL语法正确，可执行"
    test_method: "功能测试"
  - criterion: "支持常见的SQL操作（SELECT、JOIN、GROUP BY、ORDER BY等）"
    test_method: "功能测试"

implementation_hints:
  code_generation_prompt: |
    生成NL2SQL转换系统，要求：
    1. 集成多种AI模型（DeepSeek、豆包等）
    2. 实现自然语言预处理和意图识别
    3. 支持数据库schema上下文注入
    4. 包含SQL生成和后处理逻辑
    5. 实现置信度评估机制
    6. 添加错误处理和重试机制
    7. 包含完整的测试用例
    8. 支持多种查询类型和复杂度
  
  key_considerations:
    - "AI模型的容错和降级处理"
    - "SQL注入防护和安全检查"
    - "查询性能优化和执行计划分析"
    - "上下文信息的有效利用"
    - "多模型结果的融合和选择"
```

### RAG检索增强 - RE007
```yaml
feature_id: "RE007"
feature_name: "RAG检索增强"
priority: "P1"
complexity: "L"
estimated_effort: "6d"
dependencies: ["RE006", "RE005"]

description: |
  基于检索增强生成(RAG)技术，通过检索相关的语义信息、
  历史查询和业务规则来增强NL2SQL的准确性和上下文理解。

technical_specs:
  rag_engine: "RAGFlow集成"
  vector_retrieval: "语义向量检索"
  knowledge_sources: ["语义原子", "元数据", "历史查询", "业务规则"]
  retrieval_strategy: "混合检索（向量+关键词+图谱）"
  
  api_endpoints:
    - method: "POST"
      path: "/api/v1/rule-engine/rag/enhance"
      description: "RAG增强查询处理"
      request_body: |
        {
          "query": "string",
          "dataSourceId": "long",
          "retrievalConfig": {
            "topK": "integer",
            "threshold": "float",
            "sources": ["string"]
          }
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "enhancedContext": "string",
            "retrievedKnowledge": ["object"],
            "confidenceScore": "float",
            "rewrittenQuery": "string"
          }
        }

business_logic:
  - step: "查询分析"
    description: "分析自然语言查询的语义和意图"
  - step: "知识检索"
    description: "从多个知识源检索相关信息"
  - step: "上下文增强"
    description: "基于检索结果增强查询上下文"
  - step: "查询重写"
    description: "基于增强上下文重写和优化查询"
  - step: "置信度计算"
    description: "计算增强后查询的置信度"

acceptance_criteria:
  - criterion: "RAG增强后查询准确率提升10%以上"
    test_method: "对比测试"
  - criterion: "知识检索响应时间小于1秒"
    test_method: "性能测试"
  - criterion: "支持多种知识源的融合检索"
    test_method: "功能测试"
  - criterion: "上下文增强效果明显，查询理解更准确"
    test_method: "质量测试"

implementation_hints:
  code_generation_prompt: |
    生成RAG检索增强系统，要求：
    1. 集成RAGFlow检索引擎
    2. 实现多源知识检索和融合
    3. 支持向量检索和语义匹配
    4. 包含查询重写和上下文增强
    5. 实现置信度评估机制
    6. 添加检索结果排序和过滤
    7. 包含性能优化和缓存机制
    8. 包含完整的测试用例
  
  key_considerations:
    - "检索结果的相关性和质量控制"
    - "多源知识的权重平衡和融合策略"
    - "检索性能优化和缓存机制"
    - "上下文长度控制和信息压缩"
    - "检索失败的降级处理"
```

## 📊 数据模型 (Data Models)

### 核心实体定义
```typescript
// 查询记录实体
interface QueryRecord {
  id: number;
  userId: number;
  naturalQuery: string;
  generatedSql: string;
  dataSourceId: number;
  model: string;
  processingMode: string; // DIRECT, TWO_STAGE, RAG_ENHANCED
  executionTime: number;
  resultCount: number;
  confidence: number;
  status: string; // SUCCESS, FAILED, TIMEOUT
  errorMessage: string;
  createTime: Date;
}

// SQL模板实体
interface SqlTemplate {
  id: number;
  name: string;
  pattern: string;
  template: string;
  description: string;
  category: string;
  usageCount: number;
  successRate: number;
  createTime: Date;
  updateTime: Date;
}

// 语义表达式实体
interface SemanticExpression {
  id: number;
  queryId: number;
  expression: string;
  semanticType: string;
  confidence: number;
  validationStatus: string;
  createTime: Date;
}

// AI模型配置实体
interface AiModelConfig {
  id: number;
  modelName: string;
  modelType: string;
  apiEndpoint: string;
  apiKey: string; // 加密存储
  parameters: object;
  status: number; // 0-启用 1-禁用
  priority: number;
  maxTokens: number;
  temperature: number;
  createTime: Date;
  updateTime: Date;
}

// 查询缓存实体
interface QueryCache {
  id: number;
  queryHash: string;
  naturalQuery: string;
  generatedSql: string;
  dataSourceId: number;
  resultData: string; // JSON格式
  hitCount: number;
  lastHitTime: Date;
  expireTime: Date;
  createTime: Date;
}
```

### API设计规范
```yaml
# RESTful API 设计标准
api_base_url: "http://localhost:8082"
authentication: "Bearer Token (JWT)"
rate_limiting: "50 requests/minute per user"

# 统一响应格式
response_format:
  success: |
    {
      "code": 0,
      "data": {...},
      "msg": "查询成功",
      "timestamp": "2025-01-04T10:00:00Z"
    }
  error: |
    {
      "code": 500,
      "data": null,
      "msg": "查询失败",
      "timestamp": "2025-01-04T10:00:00Z"
    }

# 核心API端点
api_endpoints:
  nl2sql_core:
    - "POST /api/v1/rule-engine/nl2sql/query"
    - "POST /api/v1/rule-engine/nl2sql/validate"
    - "POST /api/v1/rule-engine/nl2sql/optimize"
    - "POST /api/v1/rule-engine/nl2sql/explain"

  two_stage_processing:
    - "POST /api/v1/rule-engine/two-stage/nl2semantic"
    - "POST /api/v1/rule-engine/two-stage/semantic2sql"
    - "POST /api/v1/rule-engine/two-stage/process"

  rag_enhancement:
    - "POST /api/v1/rule-engine/rag/enhance"
    - "POST /api/v1/rule-engine/rag/retrieve"
    - "GET /api/v1/rule-engine/rag/knowledge-sources"

  query_management:
    - "GET /api/v1/rule-engine/queries/history"
    - "GET /api/v1/rule-engine/queries/{id}"
    - "POST /api/v1/rule-engine/queries/batch"
    - "DELETE /api/v1/rule-engine/queries/{id}"

  model_management:
    - "GET /admin-api/rule-engine/models/list"
    - "POST /admin-api/rule-engine/models/config"
    - "PUT /admin-api/rule-engine/models/{id}/status"
    - "GET /admin-api/rule-engine/models/statistics"
```

## 🗓️ 实施路线图 (Implementation Roadmap)

### 迭代计划
```yaml
sprint_1:
  duration: "3 weeks"
  goal: "基础NL2SQL功能"
  deliverables:
    - feature_id: "RE001"
      status: "必须完成"
      assignee: "AI团队"
      description: "基础NL2SQL转换功能"
    - feature_id: "RE005"
      status: "必须完成"
      assignee: "后端团队"
      description: "AI模型集成框架"
    - infrastructure_setup: "完成"
      assignee: "DevOps团队"
      description: "AI服务环境搭建"

sprint_2:
  duration: "2 weeks"
  goal: "SQL处理和执行"
  deliverables:
    - feature_id: "RE002"
      status: "必须完成"
      assignee: "后端团队"
      description: "SQL验证与优化"
    - feature_id: "RE003"
      status: "必须完成"
      assignee: "后端团队"
      description: "查询结果执行"
    - feature_id: "RE004"
      status: "必须完成"
      assignee: "后端团队"
      description: "查询历史管理"

sprint_3:
  duration: "3 weeks"
  goal: "两阶段处理和工作流"
  deliverables:
    - feature_id: "RE006"
      status: "必须完成"
      assignee: "AI团队"
      description: "两阶段处理（NL2Semantic+Semantic2SQL）"
    - feature_id: "RE008"
      status: "必须完成"
      assignee: "AI团队"
      description: "Dify工作流集成"
    - integration_testing: "完成"
      assignee: "测试团队"
      description: "集成测试和性能测试"

sprint_4:
  duration: "2 weeks"
  goal: "RAG增强和优化"
  deliverables:
    - feature_id: "RE007"
      status: "必须完成"
      assignee: "AI团队"
      description: "RAG检索增强"
    - feature_id: "RE009"
      status: "必须完成"
      assignee: "后端团队"
      description: "智能查询缓存"
    - performance_optimization: "完成"
      assignee: "全体团队"
      description: "性能优化和调优"

# 里程碑检查点
milestones:
  basic_nl2sql:
    date: "2025-03-01"
    criteria: "完成基础NL2SQL功能，准确率达到80%"
    deliverables: ["NL2SQL转换", "SQL验证", "查询执行", "AI模型集成"]

  advanced_features:
    date: "2025-03-22"
    criteria: "完成两阶段处理和工作流集成"
    deliverables: ["两阶段处理", "Dify工作流", "查询历史", "缓存机制"]

  production_ready:
    date: "2025-04-05"
    criteria: "完成RAG增强，整体准确率达到85%"
    deliverables: ["RAG增强", "批量处理", "性能监控", "生产部署"]
```

### 质量保证
- **准确率要求**: 简单查询 >95%, 复杂查询 >80%, 整体 >85%
- **性能基准**: NL2SQL转换 <3秒, SQL执行 <5秒, 并发支持 100+
- **安全检查**: SQL注入防护、查询权限控制、敏感数据保护
- **稳定性**: AI服务可用性 >99%, 查询成功率 >90%

## 🤖 AI协作配置 (AI Collaboration Config)

### 代码生成上下文
```yaml
project_context:
  tech_stack: "Spring Boot + Spring AI + Dify + RAGFlow + 多AI模型"
  coding_style: "阿里巴巴Java开发规范"
  project_structure: |
    datamind-server-rule-engine/
    ├── src/main/java/com/data/platform/datamind/server/ruleengine/
    │   ├── controller/     # REST控制器
    │   ├── service/        # 业务逻辑层
    │   ├── nl2sql/         # NL2SQL核心功能
    │   ├── dify/          # Dify工作流集成
    │   ├── rag/           # RAG检索功能
    │   └── ai/            # AI模型管理

code_generation_templates:
  nl2sql_prompt: |
    生成NL2SQL转换系统，要求：
    1. 集成多种AI模型
    2. 实现自然语言预处理
    3. 支持SQL生成和验证
    4. 包含置信度评估
    5. 添加错误处理机制
    6. 支持查询优化
    7. 包含完整测试用例

  rag_prompt: |
    生成RAG检索增强系统，要求：
    1. 集成RAGFlow引擎
    2. 实现多源知识检索
    3. 支持向量和语义匹配
    4. 包含查询重写功能
    5. 实现结果融合机制
    6. 添加性能优化
    7. 包含缓存和监控
```

## 💡 质量保证清单

- [x] 所有P0功能都有明确的验收标准
- [x] 技术规格可以直接用于代码生成
- [x] API设计符合RESTful规范
- [x] 数据模型定义完整
- [x] 实施计划具有可执行性
- [x] AI协作配置完整可用

## 📚 附录

### 相关文档链接
- [服务详细指南](./GUIDE.md)
- [NL2SQL部署文档](./NL2SQL-DEPLOYMENT.md)
- [NL2SQL功能说明](./NL2SQL-README.md)
- [API接口文档](http://localhost:8082/doc.html)

### 风险评估
- **技术风险**: AI模型准确率不稳定，需要持续优化
- **性能风险**: 复杂查询处理时间长，需要优化算法
- **成本风险**: AI模型调用成本高，需要控制使用量
- **安全风险**: SQL注入和数据泄露，需要严格防护

---

**注意**: 本PRD文档专门针对DataMind Rule-Engine规则引擎服务设计，作为平台的AI核心，需要确保查询准确性和系统稳定性。
