# DataMind EMSS-Inspection 营销稽查智能体服务 - 产品需求文档 (PRD)

```yaml
# === PROJECT METADATA ===
project_name: "DataMind EMSS-Inspection 营销稽查智能体服务"
version: "v1.0"
created_date: "2025-01-04"
last_updated: "2025-01-04"
project_type: "microservice"
complexity_level: "complex"
estimated_duration: "10 weeks"
service_port: 8085
```

## 🎯 产品概览 (Product Overview)

### 核心价值主张
> AI驱动的营销稽查智能体，通过智能解析政策文档、自动生成稽查规则、可视化规则编排，实现营销合规检查的自动化和智能化

### 目标用户画像
- **主要用户**: 合规管理员、稽查专员、业务审计员、风控人员
- **使用场景**: 
  - 政策文件智能解析和要素提取
  - 营销稽查规则自动生成和管理
  - 可视化规则编排和流程设计
  - 自动化稽查任务执行和监控
  - 稽查结果分析和报告生成
- **用户痛点**: 
  - 政策文件复杂，人工解析效率低
  - 稽查规则编写困难，维护成本高
  - 稽查流程固化，缺乏灵活性
  - 稽查结果分散，难以统一管理

### 成功指标
- **北极星指标**: 稽查规则自动生成准确率 >80%
- **关键结果**: 
  - 政策解析准确率 >90%
  - 稽查执行效率提升 >50%
  - 规则维护成本降低 >40%
- **验证假设**: 
  - AI能有效解析政策文档并提取稽查要素
  - 可视化规则编排能提升稽查流程的灵活性

## 🔧 技术架构 (Technical Architecture)

### 技术栈选择
```json
{
  "ai_integration": {
    "nlp_models": ["DeepSeek", "字节豆包", "腾讯混元", "讯飞星火"],
    "document_processing": "NLP + 文档解析",
    "knowledge_extraction": "知识抽取和结构化",
    "workflow_engine": "智能体工作流"
  },
  "framework": {
    "core": "Spring Boot 2.7.18",
    "data_access": "MyBatis Plus 3.5.10.1",
    "cache": "Redis 6.0",
    "async_processing": "Spring Async"
  },
  "rule_engine": {
    "rule_definition": "自定义规则DSL",
    "rule_execution": "规则引擎",
    "visual_editor": "可视化规则编排",
    "workflow_management": "流程管理"
  },
  "data_integration": {
    "data_sources": "多数据源连接",
    "real_time_processing": "实时数据处理",
    "batch_processing": "批量数据处理"
  }
}
```

### 架构约束
- **性能要求**: 政策解析 <30秒, 规则执行 <5秒, 支持1000+规则并发执行
- **准确性要求**: 政策解析准确率 >90%, 规则生成准确率 >80%
- **安全要求**: 政策文档安全存储、稽查数据脱敏、操作审计
- **可扩展性**: 支持新政策类型、支持规则引擎扩展

## 📦 功能需求矩阵 (Feature Requirements Matrix)

### MVP版本 (P0 - 核心功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| EI001 | 政策文件管理 | 作为合规管理员，我希望管理政策文件，以便统一存储和版本控制 | M | 3d | - |
| EI002 | 政策文档解析 | 作为系统，我希望智能解析政策文档，以便提取稽查要素 | XL | 8d | EI001 |
| EI003 | 稽查要素提取 | 作为系统，我希望从政策中提取稽查要素，以便生成稽查规则 | L | 5d | EI002 |
| EI004 | 稽查规则生成 | 作为合规管理员，我希望自动生成稽查规则，以便快速建立稽查体系 | XL | 10d | EI003 |
| EI005 | 规则池管理 | 作为稽查专员，我希望管理稽查规则，以便维护规则生命周期 | M | 4d | EI004 |

### 增强版本 (P1 - 重要功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| EI006 | 可视化规则编排 | 作为稽查专员，我希望可视化编排规则，以便灵活设计稽查流程 | L | 6d | EI005 |
| EI007 | 智能稽查执行 | 作为系统，我希望自动执行稽查任务，以便实现稽查自动化 | L | 5d | EI006 |
| EI008 | 稽查结果管理 | 作为稽查专员，我希望管理稽查结果，以便跟踪问题处理 | M | 3d | EI007 |
| EI009 | 规则效果分析 | 作为合规管理员，我希望分析规则效果，以便优化稽查策略 | M | 4d | EI008 |

### 完整版本 (P2 - 增值功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| EI010 | 智能报告生成 | 作为管理层，我希望自动生成稽查报告，以便了解合规状况 | M | 4d | EI008 |
| EI011 | 规则推荐系统 | 作为稽查专员，我希望获得规则推荐，以便完善稽查体系 | L | 5d | EI009 |
| EI012 | 合规风险预警 | 作为风控人员，我希望获得风险预警，以便提前防范合规风险 | L | 4d | EI009 |

**复杂度说明**: S(Simple, 1-2天) | M(Medium, 3-5天) | L(Large, 6-10天) | XL(Extra Large, >10天)

## 📋 功能详细规格 (Detailed Specifications)

### 政策文档解析 - EI002
```yaml
feature_id: "EI002"
feature_name: "政策文档解析"
priority: "P0"
complexity: "XL"
estimated_effort: "8d"
dependencies: ["EI001"]

description: |
  使用AI技术智能解析政策文档，识别文档结构、提取关键信息、
  理解政策条款，为后续的稽查要素提取提供基础。

technical_specs:
  document_types: ["PDF", "Word", "HTML", "TXT"]
  nlp_models: ["DeepSeek", "字节豆包", "腾讯混元", "讯飞星火"]
  processing_pipeline: "文档预处理 -> 结构识别 -> 内容提取 -> 语义理解"
  output_format: "结构化JSON格式"
  
  api_endpoints:
    - method: "POST"
      path: "/admin-api/emss-inspection/policy/parse"
      description: "解析政策文档"
      request_body: |
        {
          "policyId": "long",
          "parseOptions": {
            "model": "string",
            "extractLevel": "string", // BASIC, DETAILED, COMPREHENSIVE
            "includeStructure": "boolean",
            "includeMetadata": "boolean"
          }
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "parseId": "string",
            "status": "string",
            "structure": "object",
            "content": "object",
            "metadata": "object",
            "confidence": "float"
          }
        }
    - method: "GET"
      path: "/admin-api/emss-inspection/policy/parse/{parseId}/status"
      description: "查询解析状态"
    - method: "GET"
      path: "/admin-api/emss-inspection/policy/parse/{parseId}/result"
      description: "获取解析结果"

business_logic:
  - step: "文档预处理"
    description: "清洗文档格式，提取纯文本内容"
  - step: "结构识别"
    description: "识别文档章节、条款、列表等结构"
  - step: "内容分类"
    description: "将内容分类为规则、定义、说明等类型"
  - step: "语义理解"
    description: "理解政策条款的语义和逻辑关系"
  - step: "结果结构化"
    description: "将解析结果转换为结构化格式"
  - step: "质量评估"
    description: "评估解析结果的准确性和完整性"

acceptance_criteria:
  - criterion: "支持主流文档格式的解析"
    test_method: "兼容性测试"
  - criterion: "文档结构识别准确率达到90%以上"
    test_method: "准确率测试"
  - criterion: "政策条款提取完整率达到95%以上"
    test_method: "完整性测试"
  - criterion: "解析处理时间小于30秒"
    test_method: "性能测试"
  - criterion: "解析结果格式标准化，便于后续处理"
    test_method: "格式验证测试"

implementation_hints:
  code_generation_prompt: |
    生成政策文档解析系统，要求：
    1. 集成多种AI模型进行文档理解
    2. 实现文档预处理和格式转换
    3. 支持文档结构识别和内容分类
    4. 包含语义理解和关系提取
    5. 实现解析结果的结构化输出
    6. 添加解析质量评估机制
    7. 支持异步处理和进度监控
    8. 包含完整的测试用例
  
  key_considerations:
    - "不同文档格式的兼容性处理"
    - "大文档的分块处理和内存优化"
    - "AI模型调用的容错和重试机制"
    - "解析结果的缓存和版本管理"
    - "解析过程的监控和日志记录"
```

### 稽查规则生成 - EI004
```yaml
feature_id: "EI004"
feature_name: "稽查规则生成"
priority: "P0"
complexity: "XL"
estimated_effort: "10d"
dependencies: ["EI003"]

description: |
  基于提取的稽查要素，使用AI技术自动生成可执行的稽查规则，
  包括规则逻辑、执行条件、检查标准等。

technical_specs:
  rule_types: ["数据质量规则", "业务逻辑规则", "合规检查规则", "异常检测规则"]
  rule_format: "自定义规则DSL + SQL模板"
  generation_strategy: "模板匹配 + AI生成 + 规则优化"
  validation: "规则语法验证 + 逻辑验证"
  
  api_endpoints:
    - method: "POST"
      path: "/admin-api/emss-inspection/rules/generate"
      description: "生成稽查规则"
      request_body: |
        {
          "policyId": "long",
          "extractedElements": ["object"],
          "generationOptions": {
            "ruleTypes": ["string"],
            "complexity": "string", // SIMPLE, MEDIUM, COMPLEX
            "includeSQL": "boolean",
            "autoValidate": "boolean"
          }
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "generationId": "string",
            "rules": ["object"],
            "statistics": "object",
            "validationResults": ["object"]
          }
        }
    - method: "POST"
      path: "/admin-api/emss-inspection/rules/validate"
      description: "验证规则有效性"
    - method: "POST"
      path: "/admin-api/emss-inspection/rules/optimize"
      description: "优化规则性能"

business_logic:
  - step: "要素分析"
    description: "分析稽查要素的类型和特征"
  - step: "规则模板匹配"
    description: "匹配合适的规则模板"
  - step: "AI规则生成"
    description: "使用AI模型生成规则逻辑"
  - step: "规则组合"
    description: "组合多个规则形成完整的稽查流程"
  - step: "规则验证"
    description: "验证规则的语法和逻辑正确性"
  - step: "规则优化"
    description: "优化规则的执行性能"

acceptance_criteria:
  - criterion: "规则生成准确率达到80%以上"
    test_method: "准确率测试"
  - criterion: "生成的规则语法正确，可执行"
    test_method: "语法验证测试"
  - criterion: "规则覆盖主要稽查场景"
    test_method: "覆盖率测试"
  - criterion: "规则生成时间小于60秒"
    test_method: "性能测试"
  - criterion: "生成的规则具有良好的可读性和可维护性"
    test_method: "质量评估测试"

implementation_hints:
  code_generation_prompt: |
    生成稽查规则生成系统，要求：
    1. 实现基于要素的规则模板匹配
    2. 集成AI模型进行规则逻辑生成
    3. 支持多种规则类型和复杂度
    4. 包含规则语法和逻辑验证
    5. 实现规则组合和优化
    6. 支持规则的可视化展示
    7. 添加规则质量评估机制
    8. 包含完整的测试用例
  
  key_considerations:
    - "规则模板的设计和维护"
    - "AI生成规则的质量控制"
    - "规则之间的依赖关系处理"
    - "规则执行性能的优化"
    - "规则版本管理和变更追踪"
```

## 📊 数据模型 (Data Models)

### 核心实体定义
```typescript
// 政策文件实体
interface PolicyDocument {
  id: number;
  name: string;
  type: string;
  version: string;
  filePath: string;
  fileSize: number;
  uploadTime: Date;
  parseStatus: string; // PENDING, PARSING, COMPLETED, FAILED
  parseResult: object; // JSON格式
  creatorId: number;
  createTime: Date;
  updateTime: Date;
}

// 稽查要素实体
interface InspectionElement {
  id: number;
  policyId: number;
  elementType: string; // RULE, CONDITION, THRESHOLD, PENALTY
  elementName: string;
  elementValue: string;
  description: string;
  extractionMethod: string;
  confidence: number;
  status: string;
  createTime: Date;
  updateTime: Date;
}

// 稽查规则实体
interface InspectionRule {
  id: number;
  ruleName: string;
  ruleCode: string;
  ruleType: string; // DATA_QUALITY, BUSINESS_LOGIC, COMPLIANCE, ANOMALY
  ruleCategory: string;
  ruleDescription: string;
  ruleLogic: string; // 规则DSL
  sqlTemplate: string;
  executionCondition: string;
  severity: string; // LOW, MEDIUM, HIGH, CRITICAL
  status: string; // ACTIVE, INACTIVE, TESTING
  version: string;
  policyId: number;
  creatorId: number;
  createTime: Date;
  updateTime: Date;
}

// 规则执行记录
interface RuleExecutionRecord {
  id: number;
  ruleId: number;
  executionId: string;
  dataSourceId: number;
  executionTime: Date;
  executionDuration: number;
  status: string; // SUCCESS, FAILED, TIMEOUT
  resultCount: number;
  violationCount: number;
  executionResult: string; // JSON格式
  errorMessage: string;
  createTime: Date;
}

// 稽查结果实体
interface InspectionResult {
  id: number;
  ruleId: number;
  executionId: string;
  customerId: string;
  violationType: string;
  violationLevel: string;
  violationDescription: string;
  violationData: string; // JSON格式
  processingStatus: string; // PENDING, PROCESSING, RESOLVED, IGNORED
  processingNote: string;
  processorId: number;
  processTime: Date;
  createTime: Date;
  updateTime: Date;
}
```

### API设计规范
```yaml
# RESTful API 设计标准
api_base_url: "http://localhost:8085"
authentication: "Bearer Token (JWT)"
rate_limiting: "50 requests/minute per user"

# 统一响应格式
response_format:
  success: |
    {
      "code": 0,
      "data": {...},
      "msg": "操作成功"
    }
  error: |
    {
      "code": 500,
      "data": null,
      "msg": "操作失败"
    }

# 核心API端点
api_endpoints:
  policy_management:
    - "GET /admin-api/emss-inspection/policy/page"
    - "POST /admin-api/emss-inspection/policy/upload"
    - "PUT /admin-api/emss-inspection/policy/update"
    - "DELETE /admin-api/emss-inspection/policy/delete"
    - "POST /admin-api/emss-inspection/policy/parse"

  element_extraction:
    - "GET /admin-api/emss-inspection/elements/list"
    - "POST /admin-api/emss-inspection/elements/extract"
    - "PUT /admin-api/emss-inspection/elements/update"
    - "POST /admin-api/emss-inspection/elements/validate"

  rule_management:
    - "GET /admin-api/emss-inspection/rules/page"
    - "POST /admin-api/emss-inspection/rules/generate"
    - "PUT /admin-api/emss-inspection/rules/update"
    - "DELETE /admin-api/emss-inspection/rules/delete"
    - "POST /admin-api/emss-inspection/rules/execute"

  inspection_execution:
    - "POST /admin-api/emss-inspection/execution/start"
    - "GET /admin-api/emss-inspection/execution/status/{id}"
    - "GET /admin-api/emss-inspection/execution/results"
    - "POST /admin-api/emss-inspection/execution/batch"

  result_management:
    - "GET /admin-api/emss-inspection/results/page"
    - "PUT /admin-api/emss-inspection/results/process"
    - "GET /admin-api/emss-inspection/results/statistics"
    - "POST /admin-api/emss-inspection/results/export"
```

## 🗓️ 实施路线图 (Implementation Roadmap)

### 迭代计划
```yaml
sprint_1:
  duration: "3 weeks"
  goal: "政策管理和文档解析"
  deliverables:
    - feature_id: "EI001"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "政策文件管理系统"
    - feature_id: "EI002"
      status: "必须完成"
      assignee: "AI团队"
      description: "政策文档解析功能"
    - infrastructure_setup: "完成"
      assignee: "DevOps团队"
      description: "AI服务和存储环境搭建"

sprint_2:
  duration: "2 weeks"
  goal: "要素提取和规则生成"
  deliverables:
    - feature_id: "EI003"
      status: "必须完成"
      assignee: "AI团队"
      description: "稽查要素提取"
    - feature_id: "EI004"
      status: "必须完成"
      assignee: "AI团队+后端团队"
      description: "稽查规则生成"
    - feature_id: "EI005"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "规则池管理"

sprint_3:
  duration: "3 weeks"
  goal: "可视化编排和执行引擎"
  deliverables:
    - feature_id: "EI006"
      status: "必须完成"
      assignee: "前端团队+后端团队"
      description: "可视化规则编排"
    - feature_id: "EI007"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "智能稽查执行"
    - feature_id: "EI008"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "稽查结果管理"

sprint_4:
  duration: "2 weeks"
  goal: "分析优化和增强功能"
  deliverables:
    - feature_id: "EI009"
      status: "必须完成"
      assignee: "数据团队"
      description: "规则效果分析"
    - feature_id: "EI010"
      status: "可选完成"
      assignee: "后端开发团队"
      description: "智能报告生成"
    - performance_optimization: "完成"
      assignee: "全体团队"
      description: "性能优化和测试"

# 里程碑检查点
milestones:
  basic_functionality:
    date: "2025-03-21"
    criteria: "完成政策解析和规则生成核心功能"
    deliverables: ["政策管理", "文档解析", "要素提取", "规则生成", "规则管理"]

  advanced_features:
    date: "2025-04-11"
    criteria: "完成可视化编排和执行引擎"
    deliverables: ["可视化编排", "稽查执行", "结果管理", "效果分析"]

  production_ready:
    date: "2025-04-25"
    criteria: "完成所有功能，通过用户验收测试"
    deliverables: ["智能报告", "规则推荐", "风险预警", "生产部署"]
```

### 质量保证
- **准确率要求**: 政策解析 >90%, 规则生成 >80%, 稽查执行 >95%
- **性能基准**: 政策解析 <30秒, 规则执行 <5秒, 支持1000+规则
- **安全检查**: 政策文档加密存储、稽查数据脱敏、操作审计
- **可用性**: 服务可用性 >99%, 稽查任务成功率 >95%

## 🤖 AI协作配置 (AI Collaboration Config)

### 代码生成上下文
```yaml
project_context:
  tech_stack: "Spring Boot + AI模型 + 规则引擎 + 可视化编排"
  coding_style: "阿里巴巴Java开发规范"
  project_structure: |
    datamind-server-emss-inspection/
    ├── src/main/java/com/data/platform/datamind/server/emss/
    │   ├── controller/     # REST控制器
    │   ├── service/        # 业务逻辑层
    │   ├── policy/         # 政策处理
    │   ├── rule/          # 规则管理
    │   ├── execution/     # 执行引擎
    │   └── analysis/      # 分析统计

code_generation_templates:
  policy_prompt: |
    生成政策文档处理系统，要求：
    1. 集成多种AI模型进行文档解析
    2. 实现文档结构识别和内容提取
    3. 支持多种文档格式
    4. 包含解析质量评估
    5. 添加异步处理机制
    6. 包含完整测试用例

  rule_prompt: |
    生成稽查规则管理系统，要求：
    1. 实现规则自动生成
    2. 支持规则DSL和模板
    3. 包含规则验证和优化
    4. 支持可视化规则编排
    5. 实现规则执行引擎
    6. 添加规则效果分析
```

## 💡 质量保证清单

- [x] 所有P0功能都有明确的验收标准
- [x] 技术规格可以直接用于代码生成
- [x] API设计符合RESTful规范
- [x] 数据模型定义完整
- [x] 实施计划具有可执行性
- [x] AI协作配置完整可用

## 📚 附录

### 相关文档链接
- [服务详细指南](./GUIDE.md)
- [政策处理文档](./README_POLICY_PROCESSING.md)
- [API接口文档](http://localhost:8085/doc.html)

### 风险评估
- **技术风险**: AI解析准确率不稳定，需要持续训练优化
- **业务风险**: 规则生成质量影响稽查效果，需要人工审核
- **合规风险**: 稽查规则的合规性，需要法务审核

---

**注意**: 本PRD文档专门针对DataMind EMSS-Inspection营销稽查智能体服务设计，作为合规管理的核心，需要确保规则准确性和执行可靠性。
