#!/usr/bin/env node

/**
 * DataMind Cloud 任务拆解工具
 * 用于辅助生成和管理项目任务
 */

const fs = require('fs');
const path = require('path');

class TaskBreakdownTool {
    constructor() {
        this.taskFile = path.join(process.cwd(), 'TASK.md');
        this.tasks = [];
        this.nextTaskId = 1;
    }

    /**
     * 初始化工具
     */
    init() {
        console.log('🚀 DataMind Cloud 任务拆解工具');
        console.log('================================');
        
        if (fs.existsSync(this.taskFile)) {
            console.log('✅ 发现现有TASK.md文件');
            this.loadExistingTasks();
        } else {
            console.log('📝 将创建新的TASK.md文件');
        }
    }

    /**
     * 加载现有任务
     */
    loadExistingTasks() {
        try {
            const content = fs.readFileSync(this.taskFile, 'utf8');
            const taskMatches = content.match(/\| T(\d+)\s+\|/g);
            
            if (taskMatches) {
                const taskIds = taskMatches.map(match => {
                    const id = match.match(/T(\d+)/)[1];
                    return parseInt(id);
                });
                this.nextTaskId = Math.max(...taskIds) + 1;
                console.log(`📊 已加载 ${taskIds.length} 个现有任务`);
                console.log(`🔢 下一个任务ID: T${String(this.nextTaskId).padStart(3, '0')}`);
            }
        } catch (error) {
            console.error('❌ 加载现有任务失败:', error.message);
        }
    }

    /**
     * 创建新任务
     */
    createTask(taskData) {
        const taskId = `T${String(this.nextTaskId).padStart(3, '0')}`;
        
        const task = {
            id: taskId,
            name: taskData.name || '未命名任务',
            priority: taskData.priority || 'P2',
            status: taskData.status || '⏳ 待开始',
            assignee: taskData.assignee || '待分配',
            effort: taskData.effort || '1人天',
            dependencies: taskData.dependencies || '无',
            description: taskData.description || '',
            implementation: taskData.implementation || [],
            filePaths: taskData.filePaths || [],
            keyPoints: taskData.keyPoints || [],
            acceptance: taskData.acceptance || [],
            aiHints: taskData.aiHints || '',
            notes: taskData.notes || []
        };

        this.tasks.push(task);
        this.nextTaskId++;
        
        console.log(`✅ 创建任务: ${taskId} - ${task.name}`);
        return task;
    }

    /**
     * 生成任务表格行
     */
    generateTaskTableRow(task) {
        return `| ${task.id} | ${task.name} | ${task.priority} | ${task.status} | ${task.assignee} | ${task.effort} | ${task.dependencies} |`;
    }

    /**
     * 生成详细任务描述
     */
    generateTaskDetail(task) {
        let detail = `#### 任务ID：${task.id}\n`;
        detail += `- **任务名称**：${task.name}\n`;
        
        if (task.implementation.length > 0) {
            detail += `- **技术实现**：\n`;
            task.implementation.forEach(impl => {
                detail += `  - ${impl}\n`;
            });
        }
        
        if (task.filePaths.length > 0) {
            detail += `- **文件路径**：\n`;
            task.filePaths.forEach(path => {
                detail += `  - \`${path}\`\n`;
            });
        }
        
        if (task.keyPoints.length > 0) {
            detail += `- **关键代码点**：\n`;
            task.keyPoints.forEach(point => {
                detail += `  - ${point}\n`;
            });
        }
        
        detail += `- **依赖任务**：${task.dependencies}\n`;
        detail += `- **预估工时**：${task.effort}\n`;
        detail += `- **负责人**：${task.assignee}\n`;
        
        if (task.acceptance.length > 0) {
            detail += `- **验收标准**：\n`;
            task.acceptance.forEach(criteria => {
                detail += `  - [ ] ${criteria}\n`;
            });
        }
        
        if (task.aiHints) {
            detail += `- **AI提示**：${task.aiHints}\n`;
        }
        
        if (task.notes.length > 0) {
            detail += `- **注意事项**：\n`;
            task.notes.forEach(note => {
                detail += `  - ${note}\n`;
            });
        }
        
        detail += '\n';
        return detail;
    }

    /**
     * 更新TASK.md文件
     */
    updateTaskFile() {
        if (!fs.existsSync(this.taskFile)) {
            console.log('❌ TASK.md文件不存在，请先创建基础文件');
            return;
        }

        try {
            let content = fs.readFileSync(this.taskFile, 'utf8');
            
            // 更新任务总览表格
            const tableStart = content.indexOf('| 任务ID | 任务名称 | 优先级 | 状态 | 负责人 | 预估工时 | 依赖任务 |');
            const tableEnd = content.indexOf('\n\n## 📝 详细任务拆解');
            
            if (tableStart !== -1 && tableEnd !== -1) {
                const tableHeader = '| 任务ID | 任务名称 | 优先级 | 状态 | 负责人 | 预估工时 | 依赖任务 |\n|--------|----------|--------|------|--------|----------|----------|\n';
                let tableRows = '';
                
                this.tasks.forEach(task => {
                    tableRows += this.generateTaskTableRow(task) + '\n';
                });
                
                const newTable = tableHeader + tableRows;
                content = content.substring(0, tableStart) + newTable + content.substring(tableEnd);
            }
            
            // 添加详细任务描述
            const detailStart = content.indexOf('## 📝 详细任务拆解');
            if (detailStart !== -1) {
                let detailSection = '\n## 📝 详细任务拆解\n\n';
                
                // 按优先级分组
                const priorityGroups = {
                    'P0': '### 🔥 P0 - 关键路径任务\n\n',
                    'P1': '### ⚡ P1 - 重要任务\n\n',
                    'P2': '### 🔧 P2 - 一般任务\n\n',
                    'P3': '### 🎨 P3 - 优化任务\n\n'
                };
                
                Object.keys(priorityGroups).forEach(priority => {
                    const priorityTasks = this.tasks.filter(task => task.priority === priority);
                    if (priorityTasks.length > 0) {
                        detailSection += priorityGroups[priority];
                        priorityTasks.forEach(task => {
                            detailSection += this.generateTaskDetail(task);
                        });
                    }
                });
                
                // 查找下一个主要章节的位置
                const nextSectionStart = content.indexOf('## 📊 任务依赖关系图');
                if (nextSectionStart !== -1) {
                    content = content.substring(0, detailStart) + detailSection + content.substring(nextSectionStart);
                } else {
                    content = content.substring(0, detailStart) + detailSection;
                }
            }
            
            fs.writeFileSync(this.taskFile, content, 'utf8');
            console.log('✅ TASK.md文件更新成功');
            
        } catch (error) {
            console.error('❌ 更新TASK.md文件失败:', error.message);
        }
    }

    /**
     * 显示使用帮助
     */
    showHelp() {
        console.log(`
📖 使用方法:
  node task-breakdown.js [命令] [参数]

🔧 可用命令:
  init                    - 初始化任务管理系统
  create                  - 交互式创建新任务
  list                    - 列出所有任务
  update                  - 更新TASK.md文件
  help                    - 显示帮助信息

📝 示例:
  node task-breakdown.js init
  node task-breakdown.js create
  node task-breakdown.js update

💡 提示:
  - 首次使用请运行 'init' 命令
  - 创建任务后记得运行 'update' 命令更新文件
  - 建议配合AI助手使用任务拆解提示词
        `);
    }

    /**
     * 交互式创建任务
     */
    async interactiveCreate() {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        const question = (prompt) => {
            return new Promise((resolve) => {
                rl.question(prompt, resolve);
            });
        };

        try {
            console.log('\n📝 创建新任务 (按回车使用默认值)');
            console.log('================================');
            
            const name = await question('任务名称: ');
            const priority = await question('优先级 (P0/P1/P2/P3) [P2]: ') || 'P2';
            const assignee = await question('负责人 [待分配]: ') || '待分配';
            const effort = await question('预估工时 [1人天]: ') || '1人天';
            const dependencies = await question('依赖任务 [无]: ') || '无';
            
            const taskData = {
                name,
                priority,
                assignee,
                effort,
                dependencies
            };
            
            this.createTask(taskData);
            console.log('\n✅ 任务创建成功！');
            console.log('💡 请运行 "node task-breakdown.js update" 更新TASK.md文件');
            
        } catch (error) {
            console.error('❌ 创建任务失败:', error.message);
        } finally {
            rl.close();
        }
    }

    /**
     * 列出所有任务
     */
    listTasks() {
        if (this.tasks.length === 0) {
            console.log('📝 暂无任务');
            return;
        }

        console.log('\n📋 任务列表');
        console.log('============');
        this.tasks.forEach(task => {
            console.log(`${task.id}: ${task.name} (${task.priority}, ${task.status})`);
        });
    }
}

// 主程序入口
function main() {
    const tool = new TaskBreakdownTool();
    const command = process.argv[2];

    switch (command) {
        case 'init':
            tool.init();
            break;
        case 'create':
            tool.init();
            tool.interactiveCreate();
            break;
        case 'list':
            tool.init();
            tool.listTasks();
            break;
        case 'update':
            tool.init();
            tool.updateTaskFile();
            break;
        case 'help':
        case '--help':
        case '-h':
            tool.showHelp();
            break;
        default:
            console.log('❌ 未知命令，使用 --help 查看帮助');
            tool.showHelp();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = TaskBreakdownTool;
