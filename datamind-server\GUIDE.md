# DataMind Server 主服务指南

> DataMind Cloud的核心业务服务容器，集成系统功能、基础设施服务和业务流程编排

## 服务概述

### 功能定位
DataMind Server是整个DataMind Cloud平台的主要业务服务容器，负责：
- **系统功能管理**: 用户、权限、部门、角色管理
- **基础设施服务**: 文件存储、配置管理、定时任务
- **业务流程编排**: 协调各个微服务间的业务流程
- **AI服务集成**: 集成多种AI模型和向量存储

### 技术架构
- **框架**: Spring Boot 2.7.18 + Spring Security 5.8.14
- **数据访问**: MyBatis Plus ******** + Dynamic DataSource
- **AI集成**: Spring AI + 多AI模型支持
- **向量存储**: Redis + Qdrant + Milvus
- **缓存**: Redis + Spring Cache

## 快速开始

### 环境要求
- **Java**: JDK 8+
- **Maven**: 3.6+
- **MySQL**: 5.7+ 或 8.0+
- **Redis**: 6.0+

### 本地启动
```bash
# 1. 进入服务目录
cd datamind-server

# 2. 配置数据库连接
vim src/main/resources/application-local.yaml

# 3. 启动服务
mvn spring-boot:run

# 4. 验证服务
curl http://localhost:8080/actuator/health
```

### 配置说明

#### 数据库配置
```yaml
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ******************************************************************************
          username: root
          password: your_password
```

#### AI服务配置
```yaml
spring:
  ai:
    # 向量存储配置
    vectorstore:
      redis:
        initialize-schema: true
        index: knowledge_index
        prefix: "knowledge_segment:"
      milvus:
        initialize-schema: true
        database-name: default
        collection-name: knowledge_segment
        client:
          host: 127.0.0.1
          port: 19530
    
    # AI模型配置
    qianfan: # 文心一言
      api-key: your_qianfan_api_key
      secret-key: your_qianfan_secret_key
    zhipuai: # 智谱AI
      api-key: your_zhipuai_api_key

# 自定义AI配置
datamind:
  ai:
    deep-seek:
      enable: true
      api-key: your_deepseek_api_key
      model: deepseek-chat
    doubao:
      enable: true
      api-key: your_doubao_api_key
      model: doubao-1-5-lite-32k-250115
```

## 核心功能模块

### 1. 系统管理模块
- **用户管理**: 用户CRUD、权限分配、状态管理
- **角色管理**: 角色定义、权限配置、数据范围控制
- **部门管理**: 组织架构管理、树形结构展示
- **菜单管理**: 系统菜单配置、按钮权限控制

### 2. 基础设施模块
- **文件管理**: 文件上传、下载、存储管理
- **配置管理**: 系统参数配置、动态配置更新
- **定时任务**: 任务调度、执行监控、日志管理
- **代码生成**: 基于数据库表的代码自动生成

### 3. AI服务集成
- **多模型支持**: DeepSeek、豆包、混元、星火等
- **向量存储**: Redis、Qdrant、Milvus多种向量数据库
- **智能对话**: 集成多种大语言模型
- **图像生成**: Midjourney集成

## API接口

### 系统管理API

#### 用户管理
```bash
# 获取用户列表
GET /admin-api/system/user/page

# 创建用户
POST /admin-api/system/user/create
{
  "username": "testuser",
  "nickname": "测试用户",
  "email": "<EMAIL>",
  "mobile": "13800138000",
  "deptId": 1,
  "postIds": [1],
  "roleIds": [2]
}

# 更新用户
PUT /admin-api/system/user/update
{
  "id": 1,
  "nickname": "更新昵称",
  "email": "<EMAIL>"
}
```

#### 角色管理
```bash
# 获取角色列表
GET /admin-api/system/role/page

# 分配角色权限
PUT /admin-api/system/role/update-permission
{
  "roleId": 2,
  "menuIds": [1, 2, 3, 4],
  "dataScopeType": 2
}
```

### 基础设施API

#### 文件管理
```bash
# 上传文件
POST /admin-api/infra/file/upload
Content-Type: multipart/form-data

# 获取文件列表
GET /admin-api/infra/file/page

# 删除文件
DELETE /admin-api/infra/file/delete?id=1
```

#### 配置管理
```bash
# 获取配置列表
GET /admin-api/infra/config/page

# 更新配置
PUT /admin-api/infra/config/update
{
  "id": 1,
  "configValue": "new_value"
}
```

## 开发指南

### 项目结构
```
datamind-server/
├── src/main/java/
│   └── com/data/platform/datamind/server/
│       ├── DatamindServerApplication.java    # 启动类
│       └── config/                           # 配置类
├── src/main/resources/
│   ├── application.yaml                      # 主配置文件
│   ├── application-local.yaml               # 本地环境配置
│   ├── application-dev.yaml                 # 开发环境配置
│   └── logback-spring.xml                   # 日志配置
└── pom.xml                                   # Maven配置
```

### 依赖模块
- `datamind-module-system-server`: 系统功能模块
- `datamind-module-infra-server`: 基础设施模块
- `datamind-spring-boot-starter-*`: 自定义Starter组件

### 扩展开发

#### 添加新的AI模型
```java
@Component
public class CustomAIProvider implements AIProvider {
    
    @Override
    public String getProviderName() {
        return "custom-ai";
    }
    
    @Override
    public ChatResponse chat(ChatRequest request) {
        // 实现自定义AI模型调用逻辑
        return null;
    }
}
```

#### 添加新的向量存储
```java
@Configuration
@ConditionalOnProperty(name = "spring.ai.vectorstore.custom.enabled", havingValue = "true")
public class CustomVectorStoreConfiguration {
    
    @Bean
    public VectorStore customVectorStore() {
        return new CustomVectorStore();
    }
}
```

## 部署配置

### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY target/datamind-server.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 环境变量
```bash
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=datamind
MYSQL_USERNAME=root
MYSQL_PASSWORD=password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AI服务配置
DEEPSEEK_API_KEY=your_api_key
DOUBAO_API_KEY=your_api_key
```

## 监控和运维

### 健康检查
```bash
# 服务健康状态
curl http://localhost:8080/actuator/health

# 详细健康信息
curl http://localhost:8080/actuator/health/details
```

### 性能监控
```bash
# JVM信息
curl http://localhost:8080/actuator/metrics/jvm.memory.used

# 数据库连接池
curl http://localhost:8080/actuator/metrics/hikaricp.connections.active
```

### 日志管理
```bash
# 查看应用日志
tail -f logs/datamind-server.log

# 查看错误日志
grep "ERROR" logs/datamind-server.log
```

## 故障排查

### 常见问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 启动失败 | 端口冲突 | 修改server.port配置 |
| 数据库连接失败 | 配置错误 | 检查数据库连接配置 |
| AI服务调用失败 | API密钥错误 | 检查AI服务配置 |
| 内存不足 | JVM设置过小 | 增加-Xmx参数 |

### 调试技巧
```bash
# 开启调试模式
java -jar -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005 datamind-server.jar

# 查看线程堆栈
jstack <pid>

# 查看内存使用
jmap -histo <pid>
```
