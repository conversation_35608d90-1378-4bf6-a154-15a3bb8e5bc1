# DataMind Rule-Engine 规则引擎服务指南

> AI驱动的智能查询核心引擎，支持NL2SQL、两阶段处理、RAG增强和智能体工作流

## 服务概述

### 功能定位
Rule-Engine服务是DataMind Cloud平台的AI智能查询核心，负责：
- **NL2SQL转换**: 自然语言转SQL查询语句
- **两阶段处理**: NL2Semantic + Semantic2SQL的高级处理模式
- **RAG增强**: 基于检索增强生成的智能查询优化
- **智能体工作流**: 集成Dify工作流进行复杂业务处理
- **SQL优化**: 生成的SQL语句验证、优化和解释

### 技术架构
- **AI集成**: Dify + RAGFlow + 多AI模型
- **工作流引擎**: Dify Workflow + 自定义规则引擎
- **RAG技术**: 向量检索 + 图谱检索 + 语义扩展
- **缓存优化**: Redis缓存 + 智能预热
- **监控统计**: 实时性能监控和使用统计

## 快速开始

### 环境要求
- **Java**: JDK 8+
- **AI服务**: Dify API访问权限
- **数据服务**: data-semantic服务 (RAG功能)
- **Redis**: 6.0+ (缓存)

### 本地启动
```bash
# 1. 进入服务目录
cd datamind-server-rule-engine

# 2. 配置AI服务
vim src/main/resources/application-local.yaml

# 3. 启动服务
mvn spring-boot:run -Dspring.profiles.active=local

# 4. 测试NL2SQL功能
curl http://localhost:8082/admin-api/rule-engine/nl2sql/test/demo
```

### 核心配置
```yaml
# Dify工作流配置
dify:
  api:
    base-url: https://api.dify.ai/v1
    api-key: ${DIFY_API_KEY:your-dify-api-key}
  workflow:
    nl2sql:
      app-id: ${DIFY_NL2SQL_APP_ID:your-app-id}
      timeout: 30000
      retry-count: 3
    rule-generation:
      app-id: ${DIFY_RULE_GENERATION_APP_ID:your-app-id}
      timeout: 30000
    semantic-analysis:
      app-id: ${DIFY_SEMANTIC_ANALYSIS_APP_ID:your-app-id}
      timeout: 30000

# Data-Semantic服务配置 (RAG功能)
datamind:
  data-semantic:
    service:
      base-url: ${DATA_SEMANTIC_BASE_URL:http://localhost:8083}
      api-version: v1
      connect-timeout: 5000
      read-timeout: 30000
      retry-count: 3
    rag:
      enabled: true
      default-top-k: 10
      similarity-threshold: 0.7
      enable-query-expansion: true
```

## 核心功能模块

### 1. NL2SQL转换
- **直接转换**: 自然语言直接转换为SQL语句
- **批量处理**: 支持批量处理多个查询
- **SQL验证**: 语法、语义和安全性验证
- **SQL优化**: 性能优化和查询重写
- **结果解释**: 生成SQL的自然语言解释

### 2. 两阶段NL2SQL
- **NL2Semantic**: 自然语言转语义表达式
- **Semantic2SQL**: 语义表达式转SQL语句
- **RAG增强**: 基于知识库的上下文增强
- **语义验证**: 语义完整性和一致性验证

### 3. 高级RAG技术
- **多阶段检索**: 向量检索 + 图谱检索 + 语义扩展
- **Agentic RAG**: 基于智能体的自适应检索
- **混合向量检索**: 密集向量 + 稀疏向量融合
- **智能重排序**: 上下文重排序 + 语义重排序

### 4. 智能体工作流
- **NL2SQL工作流**: 专门的自然语言转SQL工作流
- **规则生成工作流**: 业务规则自动生成
- **语义分析工作流**: 复杂语义分析处理

## API接口

### NL2SQL核心API

#### 基础转换
```bash
# 自然语言转SQL
POST /admin-api/rule-engine/nl2sql/convert
{
  "naturalLanguageQuery": "查询所有用户的姓名和邮箱",
  "databaseType": "MySQL",
  "databaseSchema": {
    "databaseName": "demo_db",
    "tables": [
      {
        "tableName": "users",
        "tableComment": "用户信息表",
        "columns": [
          {
            "columnName": "id",
            "columnType": "BIGINT",
            "columnComment": "用户ID",
            "isPrimaryKey": true
          },
          {
            "columnName": "username",
            "columnType": "VARCHAR(50)",
            "columnComment": "用户名"
          },
          {
            "columnName": "email",
            "columnType": "VARCHAR(100)",
            "columnComment": "邮箱地址"
          }
        ]
      }
    ]
  },
  "validateSql": true,
  "optimizeSql": true
}
```

#### 批量处理
```bash
# 批量NL2SQL转换
POST /admin-api/rule-engine/nl2sql/batch-convert
{
  "requests": [
    {
      "naturalLanguageQuery": "查询用户总数",
      "databaseSchema": {...}
    },
    {
      "naturalLanguageQuery": "查询活跃用户列表",
      "databaseSchema": {...}
    }
  ]
}
```

### 两阶段NL2SQL API

#### NL2Semantic (第一阶段)
```bash
# 自然语言转语义表达式
POST /admin-api/rule-engine/two-stage-nl2sql/nl2semantic
{
  "naturalLanguageQuery": "统计每个部门的员工数量",
  "databaseSchema": {...},
  "enableRAG": true,
  "ragConfig": {
    "topK": 10,
    "similarityThreshold": 0.7,
    "includeMetadata": true
  }
}

# 验证语义表达式
POST /admin-api/rule-engine/two-stage-nl2sql/validate-semantic
{
  "semanticExpression": {...},
  "databaseSchema": {...}
}
```

#### Semantic2SQL (第二阶段)
```bash
# 语义表达式转SQL
POST /admin-api/rule-engine/two-stage-nl2sql/semantic2sql
{
  "semanticExpression": {...},
  "databaseSchema": {...},
  "targetDialect": "MySQL"
}

# 生成执行计划
POST /admin-api/rule-engine/two-stage-nl2sql/execution-plan
{
  "sql": "SELECT COUNT(*) FROM users GROUP BY department_id",
  "databaseSchema": {...}
}
```

### 高级RAG检索API

#### 多阶段检索
```bash
# 多阶段RAG检索
POST /admin-api/rule-engine/advanced-rag/multi-stage-retrieval
{
  "query": "用户数据分析",
  "stages": ["vector", "graph", "semantic"],
  "topK": 20,
  "fusionMethod": "rrf"
}

# Agentic RAG检索
POST /admin-api/rule-engine/advanced-rag/agentic-retrieval
{
  "query": "销售数据统计",
  "agentConfig": {
    "maxIterations": 3,
    "adaptiveStrategy": true
  }
}
```

#### 图谱增强检索
```bash
# 图谱增强RAG检索
POST /admin-api/rule-engine/advanced-rag/graph-enhanced-retrieval
{
  "query": "客户订单关系",
  "graphTypes": ["metadata", "semantic_atoms"],
  "hopCount": 2,
  "includeRelations": true
}
```

### 工作流管理API

#### 执行工作流
```bash
# 执行NL2SQL工作流
POST /admin-api/rule-engine/workflow/nl2sql/execute
{
  "inputs": {
    "query": "查询销售数据",
    "schema": {...},
    "options": {...}
  },
  "user": "system"
}

# 获取工作流状态
GET /admin-api/rule-engine/workflow/status/{workflowRunId}

# 取消工作流执行
POST /admin-api/rule-engine/workflow/cancel/{workflowRunId}
```

### 监控统计API

#### 使用统计
```bash
# 总体统计
GET /admin-api/rule-engine/nl2sql/statistics/overall

# 性能统计
GET /admin-api/rule-engine/nl2sql/statistics/performance

# 用户统计
GET /admin-api/rule-engine/nl2sql/statistics/users

# 小时统计
GET /admin-api/rule-engine/nl2sql/statistics/hourly?hours=24
```

#### 健康检查
```bash
# Dify服务健康检查
GET /admin-api/rule-engine/dify/health/check

# 服务信息
GET /admin-api/rule-engine/dify/health/info

# RAG服务健康检查
GET /admin-api/rule-engine/advanced-rag/health
```

## 开发指南

### 项目结构
```
datamind-server-rule-engine/
├── src/main/java/
│   └── com/data/platform/datamind/server/ruleengine/
│       ├── controller/
│       │   ├── NL2SQLController.java           # NL2SQL控制器
│       │   ├── TwoStageNL2SQLController.java   # 两阶段控制器
│       │   ├── AdvancedRAGController.java      # 高级RAG控制器
│       │   └── DifyWorkflowController.java     # 工作流控制器
│       ├── service/
│       │   ├── impl/
│       │   │   ├── NL2SQLServiceImpl.java      # NL2SQL服务实现
│       │   │   ├── NL2SemanticServiceImpl.java # NL2Semantic实现
│       │   │   ├── Semantic2SQLServiceImpl.java # Semantic2SQL实现
│       │   │   └── SemanticRAGServiceImpl.java # RAG服务实现
│       │   └── DifyWorkflowService.java        # 工作流服务
│       ├── dto/                                # 数据传输对象
│       └── config/                             # 配置类
├── src/main/resources/
│   ├── application.yaml
│   └── dify/                                   # Dify配置文件
└── pom.xml
```

### 核心服务实现

#### NL2SQL服务
```java
@Service
@Slf4j
public class NL2SQLServiceImpl implements NL2SQLService {
    
    @Override
    public NL2SQLResponseDTO convertNaturalLanguageToSQL(NL2SQLRequestDTO request) {
        log.info("开始NL2SQL转换: {}", request.getNaturalLanguageQuery());
        
        try {
            // 1. 预处理查询
            String processedQuery = preprocessQuery(request.getNaturalLanguageQuery());
            
            // 2. 调用Dify工作流
            Map<String, Object> workflowInputs = buildWorkflowInputs(request);
            Map<String, Object> result = difyWorkflowService.executeNL2SQLWorkflow(
                workflowInputs, "system");
            
            // 3. 解析结果
            NL2SQLResponseDTO response = parseWorkflowResult(result);
            
            // 4. SQL验证和优化
            if (request.getValidateSql()) {
                validateSQL(response, request.getDatabaseSchema());
            }
            
            if (request.getOptimizeSql()) {
                optimizeSQL(response, request.getDatabaseSchema());
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("NL2SQL转换失败", e);
            throw new BusinessException(NL2SQL_CONVERT_FAILED, e.getMessage());
        }
    }
}
```

#### RAG增强服务
```java
@Service
@Slf4j
public class SemanticRAGServiceImpl implements SemanticRAGService {
    
    @Override
    public EnhancedContext enhanceWithRAG(String naturalLanguageQuery, 
                                         DatabaseSchemaDTO databaseSchema) {
        
        // 1. 检索相关语义原子
        List<SemanticAtom> semanticAtoms = retrieveSemanticAtoms(naturalLanguageQuery, 10);
        
        // 2. 获取适用的业务规则
        List<BusinessRule> businessRules = retrieveBusinessRules(naturalLanguageQuery, semanticAtoms);
        
        // 3. 重写查询
        String rewrittenQuery = rewriteQuery(naturalLanguageQuery, semanticAtoms, databaseSchema);
        
        // 4. 构建增强上下文
        return EnhancedContext.builder()
            .originalQuery(naturalLanguageQuery)
            .rewrittenQuery(rewrittenQuery)
            .semanticAtoms(semanticAtoms)
            .businessRules(businessRules)
            .confidenceScore(calculateConfidenceScore(semanticAtoms, businessRules))
            .build();
    }
}
```

### 扩展开发

#### 添加新的AI模型适配器
```java
@Component
public class CustomAIModelAdapter implements AIModelAdapter {
    
    @Override
    public String getModelName() {
        return "custom-model";
    }
    
    @Override
    public NL2SQLResponseDTO processNL2SQL(NL2SQLRequestDTO request) {
        // 实现自定义模型的NL2SQL逻辑
        return callCustomModel(request);
    }
    
    @Override
    public boolean isAvailable() {
        return checkModelAvailability();
    }
}
```

#### 添加新的RAG检索策略
```java
@Component
public class CustomRetrievalStrategy implements RetrievalStrategy {
    
    @Override
    public String getStrategyName() {
        return "custom-retrieval";
    }
    
    @Override
    public List<RetrievalResult> retrieve(RetrievalRequest request) {
        // 实现自定义检索逻辑
        return performCustomRetrieval(request);
    }
}
```

## 配置和优化

### 性能配置
```yaml
datamind:
  nl2sql:
    # 缓存配置
    cache:
      enabled: true
      ttl: 3600
      max-entries: 10000
    
    # 并发配置
    async:
      core-pool-size: 10
      max-pool-size: 50
      queue-capacity: 1000
    
    # 监控配置
    monitoring:
      enabled: true
      alert-threshold:
        success-rate: 0.8
        response-time: 10000
```

### AI服务优化
```yaml
dify:
  # 连接池配置
  http:
    max-connections: 100
    connection-timeout: 5000
    read-timeout: 30000
  
  # 重试配置
  retry:
    max-attempts: 3
    backoff-delay: 1000
    multiplier: 2.0
```

## 监控和运维

### 性能监控
```bash
# NL2SQL转换统计
curl http://localhost:8082/admin-api/rule-engine/nl2sql/statistics/performance

# 工作流执行统计
curl http://localhost:8082/admin-api/rule-engine/workflow/statistics

# RAG检索性能
curl http://localhost:8082/admin-api/rule-engine/advanced-rag/statistics
```

### 故障排查

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| Dify API调用失败 | API密钥错误或网络问题 | 检查API密钥和网络连接 |
| NL2SQL转换超时 | 查询复杂或模型响应慢 | 增加超时时间或优化查询 |
| RAG检索无结果 | 知识库未初始化 | 检查data-semantic服务状态 |
| 内存使用过高 | 缓存配置不当 | 调整缓存大小和TTL |
