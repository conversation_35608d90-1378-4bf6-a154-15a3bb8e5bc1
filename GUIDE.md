# DataMind Cloud 数据智能平台

> 基于Spring Cloud微服务架构的AI驱动数据治理与智能分析平台，集成NL2SQL、RAG检索、数据元数据管理等核心功能

## 快速开始

### 环境准备
```bash
# 检查Java环境
java -version  # 需要JDK 8+

# 检查Maven环境
mvn -version   # 需要Maven 3.6+

# 检查Docker环境（可选）
docker --version
docker-compose --version
```

### 本地启动
```bash
# 1. 克隆项目
git clone <repository-url>
cd datamind-cloud-mini-service

# 2. 安装依赖
mvn clean install -DskipTests

# 3. 启动核心服务（按顺序）
# 启动主服务
cd datamind-server && mvn spring-boot:run

# 启动数据元数据服务
cd datamind-server-data-meta && mvn spring-boot:run

# 启动规则引擎服务
cd datamind-server-rule-engine && mvn spring-boot:run

# 启动数据语义服务
cd datamind-server-data-semantic && mvn spring-boot:run
```

## 系统架构

### 整体架构图
DataMind Cloud采用微服务架构，专注于AI驱动的数据治理和智能分析：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   API网关层      │    │   微服务层       │
│                │    │                │    │                │
│ Vue3 + Element │───▶│ Spring Gateway │───▶│ 业务微服务集群    │
│ Admin Console  │    │ Load Balancer  │    │ + AI智能体      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                      │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据存储层     │    │   AI服务层       │    │   基础设施层     │
│                │    │                │    │                │
│ MySQL + Redis  │    │ Dify + RAGFlow │    │ Nacos + Docker  │
│ Neo4j + Milvus │    │ NL2SQL Engine  │    │ Monitoring     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心服务清单

| 服务名称 | 功能职责 | 技术栈 | 端口 | 状态 |
|---------|---------|--------|------|------|
| datamind-server | 主业务服务容器 | Spring Boot + Security | 8080 | ✅ |
| datamind-server-data-meta | 数据元数据管理 | JDBC + Neo4j | 8081 | ✅ |
| datamind-server-rule-engine | NL2SQL规则引擎 | Dify + AI Models | 8082 | ✅ |
| datamind-server-data-semantic | 语义逻辑原子管理 | RAGFlow + Vector DB | 8083 | ✅ |
| datamind-server-data-inspection | 数据巡查服务 | XXL-Job + MaxCompute | 8084 | ✅ |
| datamind-server-emss-inspection | 营销稽查智能体 | AI + Rule Engine | 8085 | ✅ |
| datamind-gateway | API网关 | Spring Cloud Gateway | 8888 | ✅ |

### 数据流向
1. **用户查询** → API网关 → 规则引擎服务
2. **NL2SQL处理** → 语义服务(RAG检索) → 元数据服务(Schema)
3. **SQL生成** → 数据检查 → 结果返回
4. **知识同步** → 向量化存储 → 图谱构建

## 技术栈概览

### 后端技术栈
- **微服务框架**: Spring Cloud Alibaba 2021.0.6.2
- **应用框架**: Spring Boot 2.7.18
- **安全框架**: Spring Security 5.8.14
- **数据访问**: MyBatis Plus 3.5.10.1 + Dynamic DataSource
- **数据库**: MySQL 8.0 + Redis 6.0 + Neo4j + Milvus
- **注册中心**: Nacos 2.3.2
- **API网关**: Spring Cloud Gateway 3.4.1
- **任务调度**: XXL-Job 2.4.0
- **AI集成**: Dify + RAGFlow + Spring AI

### 前端技术栈
- **Vue3 + Element Plus**: 现代化管理后台
- **Vue3 + Vben(Ant Design)**: 企业级管理界面
- **Vue2 + Element UI**: 兼容版本
- **UniApp**: 移动端跨平台解决方案

### 基础设施
- **容器化**: Docker + Docker Compose
- **监控**: SkyWalking + Spring Boot Admin
- **文档**: Swagger + Knife4j
- **构建工具**: Maven 3.6+

## 目录结构

```
datamind-cloud-mini-service/
├── datamind-dependencies/              # Maven依赖版本管理
├── datamind-framework/                 # 框架层扩展
│   ├── datamind-common/               # 通用工具类
│   ├── datamind-spring-boot-starter-*/ # 自定义Starter
├── datamind-gateway/                   # API网关服务
├── datamind-server/                    # 主业务服务
├── datamind-server-data-meta/          # 数据元数据管理服务
│   ├── src/main/java/.../controller/  # 控制器层
│   ├── src/main/java/.../service/     # 业务逻辑层
│   ├── src/main/java/.../dal/         # 数据访问层
├── datamind-server-rule-engine/        # NL2SQL规则引擎服务
│   ├── src/main/java/.../nl2sql/      # NL2SQL核心功能
│   ├── src/main/java/.../dify/        # Dify工作流集成
│   ├── src/main/java/.../rag/         # RAG检索功能
├── datamind-server-data-semantic/      # 语义逻辑原子服务
│   ├── src/main/java/.../ragflow/     # RAGFlow集成
│   ├── src/main/java/.../knowledge/   # 知识管理
├── datamind-server-data-inspection/    # 数据巡查服务
├── datamind-server-emss-inspection/    # 营销稽查智能体
├── datamind-module-system/             # 系统功能模块
├── datamind-module-infra/              # 基础设施模块
├── datamind-ui/                        # 前端项目集合
│   ├── datamind-ui-admin-vue3/        # Vue3管理后台
│   ├── datamind-ui-admin-vben/        # Vben管理后台
│   ├── datamind-ui-admin-vue2/        # Vue2管理后台
├── sql/                                # 数据库脚本
│   ├── mysql/                         # MySQL脚本
│   ├── postgresql/                    # PostgreSQL脚本
├── script/                             # 部署脚本
│   ├── docker/                        # Docker配置
└── docs/                               # 项目文档
```

## 核心业务模块

### 1. 数据元数据管理 (data-meta)
- **功能**: 数据库连接管理、表结构采集、元数据图谱构建
- **关键文件**: 
  - `MetaDatabaseService.java`: 数据库元数据管理
  - `MetaTableService.java`: 表元数据管理
  - `MetaColumnService.java`: 字段元数据管理
  - `MetaDataCollectService.java`: 元数据采集服务
- **API接口**: `/admin-api/data-meta/**` 和 `/web-api/data-meta/**`

### 2. NL2SQL规则引擎 (rule-engine)
- **功能**: 自然语言转SQL、两阶段处理(NL2Semantic2SQL)、RAG增强
- **关键文件**:
  - `NL2SQLService.java`: 核心NL2SQL服务
  - `NL2SemanticService.java`: 自然语言转语义表达式
  - `Semantic2SQLService.java`: 语义表达式转SQL
  - `SemanticRAGService.java`: RAG检索增强
- **API接口**: `/admin-api/rule-engine/**`

### 3. 数据语义服务 (data-semantic)
- **功能**: 语义逻辑原子管理、RAGFlow集成、知识向量化
- **关键文件**:
  - `KnowledgeVectorizationService.java`: 知识向量化
  - `KnowledgeSyncService.java`: 知识同步
  - `RAGFlowClientService.java`: RAGFlow客户端
- **API接口**: `/web-api/data-semantic/**`

### 4. 数据巡查服务 (data-inspection)
- **功能**: 定时数据质量检查、异常告警、MaxCompute集成
- **关键文件**:
  - `DataInspectionTaskService.java`: 巡查任务管理
  - `DataInspectionResultService.java`: 巡查结果管理
- **API接口**: `/admin-api/data-inspection/**`

## 开发命令

### 环境管理
```bash
# 检查环境依赖
./script/check-env.sh

# 安装项目依赖
mvn clean install -DskipTests

# 初始化数据库
mysql -u root -p < sql/mysql/datamind.sql
```

### 服务管理
```bash
# 启动所有服务（Docker方式）
cd script/docker && docker-compose up -d

# 启动指定服务
mvn spring-boot:run -pl datamind-server

# 停止所有服务
docker-compose down

# 查看服务状态
docker-compose ps
```

### 开发调试
```bash
# 开发模式启动（热重载）
mvn spring-boot:run -Dspring-boot.run.profiles=local

# 调试模式启动
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"

# 查看应用日志
tail -f logs/datamind-server.log
```

### 代码质量
```bash
# 代码格式化
mvn spotless:apply

# 代码检查
mvn checkstyle:check

# 单元测试
mvn test

# 集成测试
mvn verify -P integration-test

# 测试覆盖率
mvn jacoco:report
```

### 构建发布
```bash
# 构建所有模块
mvn clean package -DskipTests

# 构建Docker镜像
mvn clean package dockerfile:build

# 推送到镜像仓库
mvn dockerfile:push
```

## 配置管理

### 环境变量配置

| 变量名 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| `SPRING_PROFILES_ACTIVE` | 运行环境 | local | ✅ |
| `MYSQL_HOST` | MySQL主机地址 | localhost | ✅ |
| `MYSQL_PORT` | MySQL端口 | 3306 | ✅ |
| `MYSQL_DATABASE` | 数据库名 | datamind | ✅ |
| `MYSQL_USERNAME` | 数据库用户名 | root | ✅ |
| `MYSQL_PASSWORD` | 数据库密码 | - | ✅ |
| `REDIS_HOST` | Redis主机地址 | localhost | ✅ |
| `REDIS_PORT` | Redis端口 | 6379 | ✅ |
| `NACOS_SERVER_ADDR` | Nacos服务地址 | localhost:8848 | ❌ |
| `DIFY_API_KEY` | Dify API密钥 | - | ✅ |
| `DIFY_BASE_URL` | Dify服务地址 | https://api.dify.ai/v1 | ✅ |
| `RAGFLOW_API_KEY` | RAGFlow API密钥 | - | ✅ |
| `RAGFLOW_BASE_URL` | RAGFlow服务地址 | http://localhost:9380 | ✅ |

### 配置文件层级
```
application.yaml              # 基础配置
├── application-local.yaml    # 本地开发环境
├── application-dev.yaml      # 开发环境
├── application-test.yaml     # 测试环境
└── application-prod.yaml     # 生产环境
```

### 核心配置示例

#### 数据库配置
```yaml
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/${MYSQL_DATABASE:datamind}?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true
          username: ${MYSQL_USERNAME:root}
          password: ${MYSQL_PASSWORD:}
```

#### AI服务配置
```yaml
# Dify工作流配置
dify:
  api:
    base-url: ${DIFY_BASE_URL:https://api.dify.ai/v1}
    api-key: ${DIFY_API_KEY:}
  workflow:
    nl2sql:
      app-id: ${DIFY_NL2SQL_APP_ID:}
      timeout: 30000
      retry-count: 3

# RAGFlow配置
ragflow:
  api:
    base-url: ${RAGFLOW_BASE_URL:http://localhost:9380}
    api-key: ${RAGFLOW_API_KEY:}
  dataset:
    semantic-atom-dataset-name: semantic_atoms_knowledge
    metadata-dataset-name: metadata_knowledge
```

## 开发环境设置

### 系统要求
- **操作系统**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Java**: JDK 8+ (推荐JDK 11)
- **Maven**: 3.6.0+
- **Node.js**: 16.0+ (前端开发)
- **Docker**: 20.0+ (可选，用于容器化部署)

### 数据库要求
- **MySQL**: 5.7+ 或 8.0+ (主数据库)
- **Redis**: 6.0+ (缓存)
- **Neo4j**: 4.0+ (图数据库，可选)
- **Milvus**: 2.0+ (向量数据库，可选)

### 安装步骤

#### 1. 基础环境准备
```bash
# 安装Java (以Ubuntu为例)
sudo apt update
sudo apt install openjdk-11-jdk

# 验证Java安装
java -version
javac -version

# 安装Maven
sudo apt install maven
mvn -version

# 安装MySQL
sudo apt install mysql-server
sudo mysql_secure_installation

# 安装Redis
sudo apt install redis-server
redis-cli ping
```

#### 2. 项目依赖安装
```bash
# 克隆项目
git clone <repository-url>
cd datamind-cloud-mini-service

# 安装Maven依赖
mvn clean install -DskipTests

# 验证安装
mvn dependency:tree
```

#### 3. 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE datamind CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据库结构
mysql -u root -p datamind < sql/mysql/datamind.sql

# 导入初始数据
mysql -u root -p datamind < sql/mysql/init-data.sql
```

#### 4. 配置文件设置
```bash
# 复制配置模板
cp datamind-server/src/main/resources/application-local.yaml.template \
   datamind-server/src/main/resources/application-local.yaml

# 编辑配置文件
vim datamind-server/src/main/resources/application-local.yaml
```

#### 5. 环境验证
```bash
# 启动主服务
cd datamind-server
mvn spring-boot:run

# 检查服务状态
curl http://localhost:8080/actuator/health

# 检查API文档
open http://localhost:8080/doc.html
```

### 常见安装问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| Maven依赖下载失败 | 网络问题或镜像源问题 | 配置阿里云Maven镜像源 |
| 数据库连接失败 | 配置错误或权限问题 | 检查数据库配置和用户权限 |
| 端口冲突 | 端口被占用 | 修改application.yaml中的端口配置 |
| 内存不足 | JVM堆内存设置过小 | 增加-Xmx参数设置 |
| AI服务调用失败 | API密钥或网络问题 | 检查API密钥配置和网络连接 |

## API文档

### 核心API接口

#### 1. 数据元数据管理API
```bash
# 获取数据库列表
GET /admin-api/data-meta/database/page

# 采集数据库元数据
POST /admin-api/data-meta/database/collect
{
  "host": "localhost",
  "port": 3306,
  "database": "test_db",
  "username": "root",
  "password": "password"
}

# 获取表结构信息
GET /admin-api/data-meta/table/{tableId}
```

#### 2. NL2SQL转换API
```bash
# 自然语言转SQL
POST /admin-api/rule-engine/nl2sql/convert
{
  "naturalLanguageQuery": "查询所有用户的姓名和邮箱",
  "databaseType": "MySQL",
  "databaseSchema": {...},
  "validateSql": true,
  "optimizeSql": true
}

# 两阶段NL2SQL处理
POST /admin-api/rule-engine/two-stage-nl2sql/nl2semantic
{
  "naturalLanguageQuery": "统计每个部门的员工数量",
  "databaseSchema": {...},
  "enableRAG": true
}
```

#### 3. 知识检索API
```bash
# 语义原子检索
POST /web-api/data-semantic/knowledge/retrieve-semantic-atoms
{
  "query": "用户信息",
  "topK": 10,
  "similarityThreshold": 0.7
}

# 混合知识检索
POST /web-api/data-semantic/knowledge/hybrid-retrieval
{
  "query": "销售数据分析",
  "retrievalTypes": ["semantic_atoms", "metadata", "rules"],
  "topK": 20
}
```

### API认证
所有API接口都需要通过JWT Token进行认证：

```bash
# 获取访问令牌
POST /admin-api/system/auth/login
{
  "username": "admin",
  "password": "admin123"
}

# 使用令牌访问API
curl -H "Authorization: Bearer <token>" \
     http://localhost:8080/admin-api/data-meta/database/page
```

## 测试说明

### 测试策略
- **单元测试**: 覆盖率目标 > 80%
- **集成测试**: 关键业务流程端到端测试
- **性能测试**: NL2SQL转换性能基准测试
- **AI功能测试**: AI模型调用和RAG检索效果测试

### 测试结构
```
src/test/java/
├── unit/                    # 单元测试
│   ├── service/            # 服务层测试
│   ├── controller/         # 控制器测试
│   └── util/              # 工具类测试
├── integration/            # 集成测试
│   ├── api/               # API集成测试
│   ├── database/          # 数据库集成测试
│   └── ai/                # AI服务集成测试
└── performance/            # 性能测试
    ├── nl2sql/            # NL2SQL性能测试
    └── rag/               # RAG检索性能测试
```

### 运行测试
```bash
# 运行所有单元测试
mvn test

# 运行指定模块测试
mvn test -pl datamind-server-rule-engine

# 运行集成测试
mvn verify -P integration-test

# 运行性能测试
mvn test -P performance-test

# 生成测试报告
mvn jacoco:report
open target/site/jacoco/index.html
```

### 测试数据管理
- **测试数据库**: 使用H2内存数据库进行单元测试
- **Mock数据**: 使用Mockito模拟外部服务调用
- **测试容器**: 使用Testcontainers进行集成测试

## 部署指南

### 环境要求

#### 硬件要求
- **CPU**: 4核心以上 (推荐8核心)
- **内存**: 8GB以上 (推荐16GB)
- **存储**: 100GB以上SSD存储
- **网络**: 稳定的互联网连接 (AI服务调用)

#### 软件依赖
- **操作系统**: CentOS 7+, Ubuntu 18.04+, 或 Docker环境
- **Java运行时**: OpenJDK 11+ 或 Oracle JDK 11+
- **数据库**: MySQL 8.0+, Redis 6.0+
- **容器**: Docker 20.0+, Docker Compose 1.29+

### 部署架构

#### 生产环境架构
```
                    ┌─────────────────┐
                    │   Load Balancer │
                    │    (Nginx)      │
                    └─────────┬───────┘
                              │
                    ┌─────────▼───────┐
                    │   API Gateway   │
                    │  (datamind-gw)  │
                    └─────────┬───────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
┌───────▼────────┐   ┌────────▼────────┐   ┌───────▼────────┐
│  Core Services │   │  Data Services  │   │  AI Services   │
│                │   │                 │   │                │
│ datamind-server│   │ data-meta       │   │ rule-engine    │
│ system-module  │   │ data-semantic   │   │ data-inspection│
│ infra-module   │   │ emss-inspection │   │                │
└────────────────┘   └─────────────────┘   └────────────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              │
                    ┌─────────▼───────┐
                    │  Data Storage   │
                    │                 │
                    │ MySQL + Redis   │
                    │ Neo4j + Milvus  │
                    └─────────────────┘
```

### Docker部署

#### 1. 使用Docker Compose部署
```bash
# 1. 准备部署目录
mkdir -p /opt/datamind-cloud
cd /opt/datamind-cloud

# 2. 下载docker-compose.yml
wget https://raw.githubusercontent.com/your-repo/datamind-cloud/main/script/docker/docker-compose.yml

# 3. 配置环境变量
cp .env.template .env
vim .env

# 4. 启动所有服务
docker-compose up -d

# 5. 检查服务状态
docker-compose ps
docker-compose logs -f
```

#### 2. 环境变量配置 (.env文件)
```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=your_mysql_password
MYSQL_DATABASE=datamind
REDIS_PASSWORD=your_redis_password

# 应用配置
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8080

# AI服务配置
DIFY_API_KEY=your_dify_api_key
DIFY_BASE_URL=https://api.dify.ai/v1
RAGFLOW_API_KEY=your_ragflow_api_key
RAGFLOW_BASE_URL=http://ragflow:9380

# 监控配置
NACOS_SERVER_ADDR=nacos:8848
SKYWALKING_COLLECTOR_ADDR=skywalking:11800
```

#### 3. 服务健康检查
```bash
# 检查所有服务状态
docker-compose ps

# 查看特定服务日志
docker-compose logs -f datamind-server

# 检查服务健康状态
curl http://localhost:8080/actuator/health
curl http://localhost:8081/actuator/health
curl http://localhost:8082/actuator/health
```

### Kubernetes部署

#### 1. 准备Kubernetes清单文件
```yaml
# datamind-namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: datamind-cloud

---
# datamind-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: datamind-config
  namespace: datamind-cloud
data:
  application.yaml: |
    spring:
      profiles:
        active: k8s
      datasource:
        url: ********************************
        username: root
        password: ${MYSQL_PASSWORD}
```

#### 2. 部署应用
```bash
# 创建命名空间和配置
kubectl apply -f k8s/datamind-namespace.yaml
kubectl apply -f k8s/datamind-configmap.yaml

# 部署数据库
kubectl apply -f k8s/mysql-deployment.yaml
kubectl apply -f k8s/redis-deployment.yaml

# 部署应用服务
kubectl apply -f k8s/datamind-server-deployment.yaml
kubectl apply -f k8s/datamind-gateway-deployment.yaml

# 检查部署状态
kubectl get pods -n datamind-cloud
kubectl get services -n datamind-cloud
```

### 监控告警

#### 1. 系统监控
- **应用监控**: Spring Boot Actuator + Micrometer
- **JVM监控**: JVM内存、GC、线程池状态
- **数据库监控**: MySQL连接池、慢查询、锁等待
- **Redis监控**: 内存使用、命中率、连接数

#### 2. 业务监控
- **API监控**: 请求量、响应时间、错误率
- **NL2SQL监控**: 转换成功率、处理时间、AI调用次数
- **RAG检索监控**: 检索响应时间、相似度分布、缓存命中率

#### 3. 告警规则
```yaml
# Prometheus告警规则示例
groups:
  - name: datamind-cloud
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"

      - alert: NL2SQLSlowResponse
        expr: histogram_quantile(0.95, rate(nl2sql_duration_seconds_bucket[5m])) > 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "NL2SQL response time too slow"
```

## 故障排查

### 常见问题

#### 1. 服务启动问题

| 问题症状 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 服务启动失败 | 端口冲突 | 检查端口占用: `netstat -tlnp \| grep 8080` |
| 数据库连接失败 | 配置错误或网络问题 | 检查数据库配置和网络连通性 |
| 内存不足错误 | JVM堆内存设置过小 | 增加JVM参数: `-Xmx2g -Xms1g` |
| 依赖注入失败 | Bean循环依赖 | 检查@Autowired注解和Bean定义 |

#### 2. AI服务调用问题

| 问题症状 | 可能原因 | 解决方案 |
|---------|---------|---------|
| Dify API调用失败 | API密钥错误或过期 | 检查DIFY_API_KEY配置 |
| RAGFlow连接超时 | 网络问题或服务未启动 | 检查RAGFlow服务状态和网络 |
| NL2SQL转换失败 | 输入格式错误或模型问题 | 检查输入数据格式和模型配置 |
| RAG检索无结果 | 知识库未初始化 | 执行知识库向量化操作 |

#### 3. 数据库问题

| 问题症状 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 连接池耗尽 | 连接泄漏或并发过高 | 检查连接池配置和代码中的连接关闭 |
| 慢查询 | 索引缺失或查询优化不当 | 分析慢查询日志，添加必要索引 |
| 死锁 | 事务锁竞争 | 优化事务逻辑，减少锁持有时间 |
| 数据不一致 | 并发更新或事务问题 | 检查事务隔离级别和并发控制 |

### 调试工具

#### 1. 日志分析
```bash
# 查看应用日志
tail -f logs/datamind-server.log

# 查看错误日志
grep "ERROR" logs/datamind-server.log | tail -20

# 查看特定时间段日志
sed -n '/2024-01-15 10:00:00/,/2024-01-15 11:00:00/p' logs/datamind-server.log
```

#### 2. 性能分析
```bash
# JVM性能分析
jstack <pid>  # 线程堆栈
jmap -histo <pid>  # 内存对象统计
jstat -gc <pid> 1s  # GC统计

# 网络连接分析
netstat -an | grep :8080
ss -tulpn | grep :8080
```

#### 3. 数据库诊断
```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看连接状态
SHOW PROCESSLIST;

-- 查看锁等待
SELECT * FROM information_schema.INNODB_LOCKS;
SELECT * FROM information_schema.INNODB_LOCK_WAITS;
```

### 应急处理

#### 1. 服务降级
```yaml
# 启用断路器
resilience4j:
  circuitbreaker:
    instances:
      nl2sql:
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        sliding-window-size: 10
```

#### 2. 流量限制
```yaml
# 启用限流
resilience4j:
  ratelimiter:
    instances:
      api:
        limit-for-period: 100
        limit-refresh-period: 1s
        timeout-duration: 0s
```

#### 3. 紧急联系方式
- **技术负责人**: <EMAIL>
- **运维团队**: <EMAIL>
- **24小时值班**: +86-xxx-xxxx-xxxx

## 开发指南

### 编码规范

#### 1. Java编码规范
- **命名约定**:
  - 类名使用PascalCase: `UserService`, `DataMetaController`
  - 方法名使用camelCase: `getUserById()`, `collectMetadata()`
  - 常量使用UPPER_SNAKE_CASE: `MAX_RETRY_COUNT`, `DEFAULT_TIMEOUT`
  - 包名使用小写: `com.data.platform.datamind.server`

- **代码结构**:
  - Controller层: 处理HTTP请求，参数验证
  - Service层: 业务逻辑处理，事务管理
  - DAO层: 数据访问，SQL操作
  - DTO/VO层: 数据传输对象

#### 2. API设计规范
- **RESTful风格**:
  - GET `/api/users` - 获取用户列表
  - POST `/api/users` - 创建用户
  - PUT `/api/users/{id}` - 更新用户
  - DELETE `/api/users/{id}` - 删除用户

- **响应格式**:
```json
{
  "code": 0,
  "data": {...},
  "msg": "操作成功",
  "timestamp": "2024-01-15T10:30:00"
}
```

#### 3. 数据库设计规范
- **表命名**: 使用下划线分隔的小写字母 `user_info`, `meta_database`
- **字段命名**: 使用下划线分隔 `user_id`, `create_time`, `update_time`
- **索引命名**: `idx_表名_字段名` 如 `idx_user_info_email`
- **外键命名**: `fk_表名_字段名` 如 `fk_user_info_dept_id`

### Git工作流

#### 1. 分支策略
```
master (生产环境)
├── develop (开发环境)
│   ├── feature/nl2sql-enhancement (功能分支)
│   ├── feature/rag-optimization (功能分支)
│   └── bugfix/data-meta-fix (修复分支)
└── hotfix/critical-security-fix (热修复分支)
```

#### 2. 提交信息规范
```bash
# 功能开发
git commit -m "feat(rule-engine): add NL2SQL batch processing support"

# 问题修复
git commit -m "fix(data-meta): resolve database connection timeout issue"

# 文档更新
git commit -m "docs(api): update NL2SQL API documentation"

# 代码重构
git commit -m "refactor(service): optimize RAG retrieval performance"

# 测试相关
git commit -m "test(nl2sql): add unit tests for semantic conversion"
```

#### 3. 代码审查流程
1. **创建Pull Request**: 从feature分支向develop分支提交PR
2. **自动化检查**: CI/CD流水线执行代码检查和测试
3. **人工审查**: 至少需要1名高级开发者审查
4. **合并要求**: 所有检查通过且审查通过后方可合并

### 开发最佳实践

#### 1. 异常处理
```java
// 统一异常处理
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public CommonResult<?> handleBusinessException(BusinessException e) {
        return CommonResult.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(ValidationException.class)
    public CommonResult<?> handleValidationException(ValidationException e) {
        return CommonResult.error(PARAM_ERROR.getCode(), e.getMessage());
    }
}
```

#### 2. 日志记录
```java
// 使用SLF4J进行日志记录
@Slf4j
@Service
public class NL2SQLServiceImpl implements NL2SQLService {

    public NL2SQLResponseDTO convertNaturalLanguageToSQL(NL2SQLRequestDTO request) {
        log.info("开始NL2SQL转换, query: {}", request.getNaturalLanguageQuery());

        try {
            // 业务逻辑
            NL2SQLResponseDTO response = doConvert(request);
            log.info("NL2SQL转换成功, executionTime: {}ms", response.getExecutionStats().getProcessingTimeMs());
            return response;
        } catch (Exception e) {
            log.error("NL2SQL转换失败, query: {}, error: {}", request.getNaturalLanguageQuery(), e.getMessage(), e);
            throw new BusinessException(NL2SQL_CONVERT_FAILED, e.getMessage());
        }
    }
}
```

#### 3. 缓存使用
```java
// 使用Spring Cache注解
@Service
public class MetaDataService {

    @Cacheable(value = "metadata", key = "#databaseId")
    public List<MetaTableDO> getTablesByDatabaseId(Long databaseId) {
        return metaTableMapper.selectByDatabaseId(databaseId);
    }

    @CacheEvict(value = "metadata", key = "#databaseId")
    public void refreshMetadata(Long databaseId) {
        // 刷新元数据
    }
}
```

## 扩展开发

### 1. 添加新的AI模型支持

#### 步骤1: 创建模型适配器
```java
@Component
public class CustomAIModelAdapter implements AIModelAdapter {

    @Override
    public String getModelName() {
        return "custom-model";
    }

    @Override
    public NL2SQLResponseDTO processNL2SQL(NL2SQLRequestDTO request) {
        // 实现自定义模型的NL2SQL逻辑
        return null;
    }
}
```

#### 步骤2: 注册模型适配器
```java
@Configuration
public class AIModelConfiguration {

    @Bean
    public AIModelRegistry aiModelRegistry(List<AIModelAdapter> adapters) {
        AIModelRegistry registry = new AIModelRegistry();
        adapters.forEach(registry::registerAdapter);
        return registry;
    }
}
```

### 2. 扩展数据源支持

#### 步骤1: 实现数据源连接器
```java
@Component
public class CustomDatabaseConnector implements DatabaseConnector {

    @Override
    public String getDatabaseType() {
        return "CUSTOM_DB";
    }

    @Override
    public Connection createConnection(DatabaseConfig config) {
        // 实现自定义数据库连接逻辑
        return null;
    }

    @Override
    public List<MetaTableDO> collectTables(Connection connection) {
        // 实现表结构采集逻辑
        return null;
    }
}
```

### 3. 添加新的RAG检索策略

#### 步骤1: 实现检索策略
```java
@Component
public class CustomRetrievalStrategy implements RetrievalStrategy {

    @Override
    public String getStrategyName() {
        return "custom-retrieval";
    }

    @Override
    public List<RetrievalResult> retrieve(RetrievalRequest request) {
        // 实现自定义检索逻辑
        return null;
    }
}
```

## 性能优化

### 1. 数据库优化
- **连接池配置**: 合理设置连接池大小和超时时间
- **索引优化**: 为常用查询字段添加索引
- **查询优化**: 避免N+1查询，使用批量操作
- **分页查询**: 大数据量查询使用分页

### 2. 缓存优化
- **多级缓存**: 本地缓存 + Redis分布式缓存
- **缓存预热**: 系统启动时预加载热点数据
- **缓存更新**: 使用事件驱动的缓存更新策略

### 3. AI服务优化
- **请求合并**: 批量处理多个NL2SQL请求
- **结果缓存**: 缓存常用查询的转换结果
- **异步处理**: 长时间AI调用使用异步处理
- **降级策略**: AI服务不可用时的降级方案

## 安全规范

### 1. 认证授权
- **JWT Token**: 使用JWT进行用户认证
- **权限控制**: 基于RBAC的细粒度权限控制
- **API安全**: 所有API接口都需要认证

### 2. 数据安全
- **敏感数据加密**: 数据库密码等敏感信息加密存储
- **SQL注入防护**: 使用参数化查询防止SQL注入
- **XSS防护**: 前端输入验证和输出编码

### 3. 网络安全
- **HTTPS**: 生产环境强制使用HTTPS
- **防火墙**: 配置防火墙规则限制访问
- **访问日志**: 记录所有API访问日志

## 贡献指南

### 1. 开发环境搭建
1. Fork项目到个人仓库
2. 克隆个人仓库到本地
3. 按照开发环境设置章节配置环境
4. 创建feature分支进行开发

### 2. 提交代码
1. 确保代码符合编码规范
2. 添加必要的单元测试
3. 更新相关文档
4. 提交Pull Request

### 3. 问题反馈
- **Bug报告**: 使用GitHub Issues报告问题
- **功能建议**: 通过Issues提出新功能建议
- **技术讨论**: 参与GitHub Discussions讨论

## 许可证

本项目采用 [MIT License](LICENSE) 开源协议，个人与企业可100%免费使用。

## 联系我们

- **项目主页**: https://github.com/your-org/datamind-cloud
- **文档网站**: https://docs.datamind-cloud.com
- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>

---

**DataMind Cloud** - 让数据更智能，让AI更实用 🚀
