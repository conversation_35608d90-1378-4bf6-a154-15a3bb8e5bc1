# DataMind Data-Semantic 数据语义服务指南

> 语义逻辑原子与关系管理服务，基于RAGFlow的知识向量化和智能检索平台

## 服务概述

### 功能定位
Data-Semantic服务是DataMind Cloud平台的语义知识管理核心，负责：
- **语义逻辑原子管理**: 语义原子的定义、存储和管理
- **知识向量化**: 基于RAGFlow的多类型知识向量化
- **智能检索**: 高性能的语义检索和知识发现
- **知识图谱**: 语义原子关系图谱构建和查询
- **RAG服务**: 为其他服务提供检索增强生成支持

### 技术架构
- **RAG引擎**: RAGFlow + 自定义检索策略
- **向量存储**: Milvus + Redis向量索引
- **知识管理**: 六大专业知识数据集
- **同步机制**: 实时和批量知识同步
- **API服务**: RESTful API + 异步处理

## 快速开始

### 环境要求
- **Java**: JDK 8+
- **RAGFlow**: RAGFlow服务实例
- **向量数据库**: Milvus 2.0+ (推荐)
- **Redis**: 6.0+ (缓存和向量索引)

### 本地启动
```bash
# 1. 进入服务目录
cd datamind-server-data-semantic

# 2. 配置RAGFlow连接
vim src/main/resources/application-ragflow.yml

# 3. 启动服务
mvn spring-boot:run -Dspring.profiles.active=local,ragflow

# 4. 初始化知识数据集
curl -X POST http://localhost:8083/web-api/data-semantic/knowledge/init-datasets
```

### 核心配置
```yaml
# RAGFlow配置
ragflow:
  api:
    base-url: http://localhost:9380
    api-key: ${RAGFLOW_API_KEY:your-ragflow-api-key}
    timeout: 30000
    retry-count: 3
  
  # 六大知识数据集配置
  dataset:
    semantic-atom-dataset-name: semantic_atoms_knowledge
    semantic-atom-relation-dataset-name: semantic_atom_relations_knowledge
    metadata-dataset-name: metadata_knowledge
    metadata-relation-dataset-name: metadata_relations_knowledge
    policy-document-dataset-name: policy_documents_knowledge
    inspection-rule-dataset-name: inspection_rules_knowledge
  
  # 解析器配置
  parser:
    delimiter: "\\n!?;。；！？"
    layout-recognize: true
    entity-types:
      - semantic_atom
      - metadata_table
      - metadata_field
      - rule
      - policy
```

## 核心功能模块

### 1. 六大知识数据集

#### 语义原子知识库
- **数据集名**: `semantic_atoms_knowledge`
- **内容**: 语义原子定义、属性、使用场景
- **分块方法**: knowledge-graph
- **用途**: NL2SQL语义理解和转换

#### 语义原子关系知识库
- **数据集名**: `semantic_atom_relations_knowledge`
- **内容**: 语义原子间的依赖、派生、冲突关系
- **分块方法**: knowledge-graph
- **用途**: 语义关系推理和验证

#### 元数据知识库
- **数据集名**: `metadata_knowledge`
- **内容**: 数据表、字段、血缘关系等元数据信息
- **分块方法**: knowledge-graph
- **用途**: 数据结构理解和查询优化

#### 元数据关系知识库
- **数据集名**: `metadata_relations_knowledge`
- **内容**: 表间关系、字段关系、血缘关系
- **分块方法**: knowledge-graph
- **用途**: 数据血缘分析和关系发现

#### 政策文件知识库
- **数据集名**: `policy_documents_knowledge`
- **内容**: 政策文档、法规、标准等文件内容
- **分块方法**: laws
- **用途**: 合规检查和政策理解

#### 稽查规则知识库
- **数据集名**: `inspection_rules_knowledge`
- **内容**: 稽查规则、检查逻辑、处理流程
- **分块方法**: qa
- **用途**: 智能稽查和规则应用

### 2. 知识向量化服务
- **单个向量化**: 支持单个知识对象的向量化
- **批量向量化**: 高效的批量处理能力
- **增量更新**: 支持知识的增量向量化
- **多格式支持**: 文本、JSON、结构化数据

### 3. 智能检索服务
- **语义检索**: 基于向量相似度的语义检索
- **混合检索**: 多种检索策略的融合
- **重排序**: 智能的检索结果重排序
- **缓存优化**: 高频查询结果缓存

### 4. 知识同步服务
- **自动同步**: 监听数据变更并自动同步
- **手动同步**: 支持手动触发同步操作
- **全量同步**: 支持全量重建知识索引
- **异步处理**: 所有同步操作均为异步执行

## API接口

### 知识检索API

#### 语义原子检索
```bash
# 检索语义原子
POST /web-api/data-semantic/knowledge/retrieve-semantic-atoms
{
  "query": "用户信息",
  "topK": 10,
  "similarityThreshold": 0.7,
  "includeRelations": true
}

# 批量检索语义原子
POST /web-api/data-semantic/knowledge/batch-retrieve-semantic-atoms
{
  "queries": ["用户信息", "订单数据", "销售统计"],
  "topK": 5,
  "similarityThreshold": 0.7
}
```

#### 元数据检索
```bash
# 检索元数据
POST /web-api/data-semantic/knowledge/retrieve-metadata
{
  "query": "用户表结构",
  "topK": 10,
  "includeColumns": true,
  "databaseType": "MySQL"
}

# 检索元数据关系
POST /web-api/data-semantic/knowledge/retrieve-metadata-relations
{
  "query": "用户订单关系",
  "topK": 10,
  "relationTypes": ["foreign_key", "reference"]
}
```

#### 混合检索
```bash
# 混合知识检索
POST /web-api/data-semantic/knowledge/hybrid-retrieval
{
  "query": "销售数据分析",
  "retrievalTypes": ["semantic_atoms", "metadata", "rules"],
  "topK": 20,
  "fusionMethod": "rrf",
  "weights": {
    "semantic_atoms": 0.4,
    "metadata": 0.4,
    "rules": 0.2
  }
}
```

### 知识向量化API

#### 语义原子向量化
```bash
# 单个语义原子向量化
POST /web-api/data-semantic/knowledge/vectorize-semantic-atom
{
  "atomId": "atom_001",
  "name": "用户基本信息",
  "description": "包含用户ID、姓名、邮箱等基本信息",
  "category": "用户管理",
  "attributes": {
    "dataType": "object",
    "fields": ["id", "name", "email"]
  }
}

# 批量语义原子向量化
POST /web-api/data-semantic/knowledge/batch-vectorize-semantic-atoms
{
  "atoms": [
    {
      "atomId": "atom_001",
      "name": "用户基本信息",
      "description": "..."
    },
    {
      "atomId": "atom_002", 
      "name": "订单信息",
      "description": "..."
    }
  ]
}
```

#### 元数据向量化
```bash
# 元数据向量化
POST /web-api/data-semantic/knowledge/vectorize-metadata
{
  "databaseId": 1,
  "tableId": 10,
  "tableName": "users",
  "tableComment": "用户信息表",
  "columns": [
    {
      "columnName": "id",
      "columnType": "BIGINT",
      "columnComment": "用户ID"
    }
  ]
}
```

#### 政策文件向量化
```bash
# 政策文件向量化
POST /web-api/data-semantic/knowledge/vectorize-policy-document
{
  "documentId": "policy_001",
  "title": "数据安全管理规定",
  "content": "为了规范数据安全管理...",
  "category": "数据安全",
  "effectiveDate": "2024-01-01"
}
```

### 知识同步API

#### 自动同步控制
```bash
# 启用自动同步
POST /web-api/data-semantic/sync/enable-auto-sync

# 禁用自动同步
POST /web-api/data-semantic/sync/disable-auto-sync

# 获取同步状态
GET /web-api/data-semantic/sync/status
```

#### 手动同步
```bash
# 同步语义原子
POST /web-api/data-semantic/sync/semantic-atoms
{
  "atomIds": ["atom_001", "atom_002"],
  "forceUpdate": false
}

# 同步元数据
POST /web-api/data-semantic/sync/metadata
{
  "databaseIds": [1, 2],
  "includeRelations": true
}

# 全量同步
POST /web-api/data-semantic/sync/full-sync
{
  "dataTypes": ["semantic_atoms", "metadata", "policies"],
  "rebuildIndex": true
}
```

### 数据集管理API

#### 数据集操作
```bash
# 初始化所有数据集
POST /web-api/data-semantic/knowledge/init-datasets

# 获取数据集信息
GET /web-api/data-semantic/knowledge/dataset-info?datasetName=semantic_atoms_knowledge

# 清理数据集
DELETE /web-api/data-semantic/knowledge/cleanup-dataset?datasetName=metadata_knowledge

# 重建索引
POST /web-api/data-semantic/knowledge/rebuild-index
{
  "datasetNames": ["semantic_atoms_knowledge", "metadata_knowledge"]
}
```

#### 统计信息
```bash
# 获取知识库统计
GET /web-api/data-semantic/knowledge/statistics

# 获取检索统计
GET /web-api/data-semantic/knowledge/retrieval-statistics?timeRange=24h

# 获取向量化统计
GET /web-api/data-semantic/knowledge/vectorization-statistics
```

## 开发指南

### 项目结构
```
datamind-server-data-semantic/
├── src/main/java/
│   └── com/data/platform/datamind/server/datasemantic/
│       ├── controller/
│       │   ├── KnowledgeRetrievalController.java    # 知识检索控制器
│       │   └── KnowledgeSyncController.java         # 知识同步控制器
│       ├── service/
│       │   ├── impl/
│       │   │   ├── KnowledgeVectorizationServiceImpl.java  # 向量化服务
│       │   │   ├── KnowledgeSyncServiceImpl.java           # 同步服务
│       │   │   └── RAGFlowClientServiceImpl.java           # RAGFlow客户端
│       │   └── interfaces/
│       ├── dto/                                     # 数据传输对象
│       ├── config/
│       │   ├── RAGFlowConfig.java                   # RAGFlow配置
│       │   └── RestTemplateConfig.java              # HTTP客户端配置
│       └── util/                                    # 工具类
├── src/main/resources/
│   ├── application.yaml
│   └── application-ragflow.yml                     # RAGFlow专用配置
└── pom.xml
```

### 核心服务实现

#### 知识向量化服务
```java
@Service
@Slf4j
public class KnowledgeVectorizationServiceImpl implements KnowledgeVectorizationService {
    
    @Override
    public VectorizationResult vectorizeSemanticAtom(SemanticAtomVectorizeRequest request) {
        try {
            // 1. 构建文档内容
            String documentContent = buildSemanticAtomDocument(request);
            
            // 2. 调用RAGFlow API进行向量化
            RAGFlowDocumentDTO document = RAGFlowDocumentDTO.builder()
                .name(request.getName())
                .content(documentContent)
                .metadata(buildMetadata(request))
                .build();
            
            // 3. 上传到指定数据集
            String documentId = ragFlowClientService.uploadDocument(
                ragFlowConfig.getDataset().getSemanticAtomDatasetName(),
                document
            );
            
            // 4. 返回结果
            return VectorizationResult.builder()
                .success(true)
                .documentId(documentId)
                .message("语义原子向量化成功")
                .build();
                
        } catch (Exception e) {
            log.error("语义原子向量化失败", e);
            return VectorizationResult.builder()
                .success(false)
                .message("向量化失败: " + e.getMessage())
                .build();
        }
    }
}
```

#### RAGFlow客户端服务
```java
@Service
@Slf4j
public class RAGFlowClientServiceImpl implements RAGFlowClientService {
    
    @Override
    public List<RAGFlowRetrievalResultDTO> retrieveKnowledge(RAGFlowRetrievalRequestDTO request) {
        try {
            // 1. 构建检索请求
            Map<String, Object> requestBody = Map.of(
                "question", request.getQuery(),
                "datasets", request.getDatasetNames(),
                "top_k", request.getTopK(),
                "similarity_threshold", request.getSimilarityThreshold()
            );
            
            // 2. 调用RAGFlow检索API
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(ragFlowConfig.getApi().getApiKey());
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<RAGFlowApiResponse> response = restTemplate.exchange(
                ragFlowConfig.getApi().getBaseUrl() + "/api/v1/retrieval",
                HttpMethod.POST,
                entity,
                RAGFlowApiResponse.class
            );
            
            // 3. 解析结果
            return parseRetrievalResults(response.getBody());
            
        } catch (Exception e) {
            log.error("RAGFlow检索失败", e);
            throw new BusinessException(RAGFLOW_RETRIEVAL_FAILED, e.getMessage());
        }
    }
}
```

### 扩展开发

#### 添加新的知识类型
```java
@Component
public class CustomKnowledgeVectorizer implements KnowledgeVectorizer {
    
    @Override
    public String getKnowledgeType() {
        return "custom_knowledge";
    }
    
    @Override
    public VectorizationResult vectorize(Object knowledgeObject) {
        // 实现自定义知识类型的向量化逻辑
        return performCustomVectorization(knowledgeObject);
    }
    
    @Override
    public String getTargetDataset() {
        return "custom_knowledge_dataset";
    }
}
```

#### 添加新的检索策略
```java
@Component
public class CustomRetrievalStrategy implements RetrievalStrategy {
    
    @Override
    public String getStrategyName() {
        return "custom-strategy";
    }
    
    @Override
    public List<RetrievalResult> retrieve(RetrievalRequest request) {
        // 实现自定义检索策略
        return performCustomRetrieval(request);
    }
}
```

## 配置和优化

### RAGFlow优化配置
```yaml
ragflow:
  # 性能优化
  performance:
    batch-size: 100           # 批量处理大小
    parallel-threads: 4       # 并行线程数
    timeout: 60000           # 超时时间
    retry-count: 3           # 重试次数
  
  # 缓存配置
  cache:
    enabled: true
    ttl: 3600               # 缓存TTL(秒)
    max-entries: 10000      # 最大缓存条目
  
  # 检索优化
  retrieval:
    default-top-k: 10
    max-top-k: 100
    similarity-threshold: 0.7
    enable-reranking: true
```

### 向量存储优化
```yaml
spring:
  ai:
    vectorstore:
      milvus:
        # 集合配置
        collection-config:
          dimension: 1536
          metric-type: "COSINE"
          index-type: "IVF_FLAT"
          nlist: 1024
        
        # 搜索配置
        search-config:
          nprobe: 10
          ef: 64
```

## 监控和运维

### 性能监控
```bash
# 检索性能统计
curl http://localhost:8083/web-api/data-semantic/knowledge/retrieval-statistics

# 向量化统计
curl http://localhost:8083/web-api/data-semantic/knowledge/vectorization-statistics

# RAGFlow连接状态
curl http://localhost:8083/actuator/health/ragflow
```

### 故障排查

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| RAGFlow连接失败 | 服务未启动或配置错误 | 检查RAGFlow服务状态和配置 |
| 向量化失败 | 文档格式错误或内容过大 | 检查文档内容和大小限制 |
| 检索无结果 | 数据集未初始化或为空 | 执行数据集初始化和数据同步 |
| 性能下降 | 缓存失效或并发过高 | 调整缓存配置和并发参数 |
