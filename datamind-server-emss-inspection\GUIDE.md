# DataMind EMSS-Inspection 营销稽查智能体服务指南

> AI驱动的营销稽查智能体，支持政策文件处理、稽查要素提取、规则池管理和可视化规则编排

## 服务概述

### 功能定位
EMSS-Inspection服务是DataMind Cloud平台的营销稽查智能体核心，负责：
- **政策文件处理**: 智能解析政策文档，提取稽查要素
- **稽查规则生成**: 基于AI的智能规则生成和管理
- **规则池管理**: 稽查规则的生命周期管理
- **可视化规则编排**: 拖拽式规则编排和流程设计
- **智能稽查执行**: 自动化的稽查任务执行和结果分析

### 技术架构
- **AI智能体**: 多AI模型集成 + 智能体工作流
- **规则引擎**: 自定义规则引擎 + 可视化编排
- **文档处理**: NLP + 文档解析 + 知识抽取
- **工作流引擎**: 可视化流程设计 + 自动化执行
- **数据集成**: 多数据源连接 + 实时数据处理

## 快速开始

### 环境要求
- **Java**: JDK 8+
- **AI服务**: 多AI模型API访问权限
- **文档处理**: NLP服务支持
- **数据源**: 营销业务数据库访问权限

### 本地启动
```bash
# 1. 进入服务目录
cd datamind-server-emss-inspection

# 2. 配置AI服务和数据源
vim src/main/resources/application-local.yaml

# 3. 启动服务
mvn spring-boot:run -Dspring.profiles.active=local

# 4. 访问管理界面
open http://localhost:8085/doc.html
```

### 核心配置
```yaml
# AI服务配置
datamind:
  ai:
    # 政策文件解析AI
    policy-parser:
      enable: true
      model: deepseek-chat
      api-key: ${POLICY_PARSER_API_KEY:your-api-key}
    
    # 规则生成AI
    rule-generator:
      enable: true
      model: doubao-1-5-lite-32k
      api-key: ${RULE_GENERATOR_API_KEY:your-api-key}
    
    # 稽查分析AI
    inspection-analyzer:
      enable: true
      model: hunyuan-turbo
      api-key: ${INSPECTION_ANALYZER_API_KEY:your-api-key}

# 营销稽查配置
emss:
  inspection:
    # 政策文件处理
    policy:
      upload-path: ./uploads/policies
      supported-formats: [".pdf", ".doc", ".docx", ".txt"]
      max-file-size: 50MB
    
    # 规则引擎配置
    rule-engine:
      max-rules-per-pool: 1000
      rule-execution-timeout: 30000
      parallel-execution: true
    
    # 稽查任务配置
    task:
      max-concurrent-tasks: 10
      result-retention-days: 90
```

## 核心功能模块

### 1. 政策文件处理
- **文件上传**: 支持PDF、Word、文本等多种格式
- **智能解析**: 基于NLP的政策内容理解和结构化
- **要素提取**: 自动提取稽查要素、规则条件、处罚标准
- **知识图谱**: 构建政策知识图谱和关联关系

### 2. 稽查规则管理
- **规则生成**: 基于政策文件的智能规则生成
- **规则编辑**: 可视化的规则编辑和配置界面
- **规则验证**: 规则逻辑验证和冲突检测
- **版本管理**: 规则版本控制和变更历史

### 3. 规则池管理
- **规则分类**: 按业务领域、适用范围分类管理
- **规则组合**: 复杂规则的组合和编排
- **规则优先级**: 规则执行优先级和冲突解决
- **规则生命周期**: 规则的启用、停用、归档管理

### 4. 可视化规则编排
- **拖拽式设计**: 可视化的规则流程设计
- **条件分支**: 复杂的条件判断和分支逻辑
- **动作定义**: 稽查动作和处理流程定义
- **流程模拟**: 规则流程的模拟执行和测试

### 5. 智能稽查执行
- **自动化稽查**: 基于规则的自动化稽查执行
- **实时监控**: 稽查任务的实时监控和状态跟踪
- **结果分析**: 稽查结果的智能分析和报告生成
- **异常处理**: 稽查异常的智能识别和处理建议

## API接口

### 政策文件处理API

#### 文件管理
```bash
# 上传政策文件
POST /admin-api/emss-inspection/policy/upload
Content-Type: multipart/form-data
{
  "file": <policy-document>,
  "title": "营销管理办法",
  "category": "营销管理",
  "effectiveDate": "2024-01-01",
  "description": "营销业务管理相关政策"
}

# 获取政策文件列表
GET /admin-api/emss-inspection/policy/page?pageNo=1&pageSize=10

# 获取政策文件详情
GET /admin-api/emss-inspection/policy/get?id=1

# 删除政策文件
DELETE /admin-api/emss-inspection/policy/delete?id=1
```

#### 政策解析
```bash
# 解析政策文件
POST /admin-api/emss-inspection/policy/parse
{
  "policyId": 1,
  "parseOptions": {
    "extractRules": true,
    "extractElements": true,
    "buildKnowledgeGraph": true
  }
}

# 获取解析结果
GET /admin-api/emss-inspection/policy/parse-result?policyId=1

# 提取稽查要素
POST /admin-api/emss-inspection/policy/extract-elements
{
  "policyId": 1,
  "elementTypes": ["检查项", "判断条件", "处罚标准"]
}
```

### 稽查规则管理API

#### 规则CRUD操作
```bash
# 创建稽查规则
POST /admin-api/emss-inspection/rule/create
{
  "ruleName": "客户信息完整性检查",
  "ruleType": "DATA_COMPLETENESS",
  "category": "客户管理",
  "priority": 1,
  "conditions": [
    {
      "field": "customer.name",
      "operator": "IS_NOT_NULL",
      "value": null
    },
    {
      "field": "customer.phone",
      "operator": "MATCHES_PATTERN",
      "value": "^1[3-9]\\d{9}$"
    }
  ],
  "actions": [
    {
      "type": "ALERT",
      "severity": "MEDIUM",
      "message": "客户信息不完整"
    }
  ],
  "description": "检查客户基本信息的完整性"
}

# 获取规则列表
GET /admin-api/emss-inspection/rule/page?category=客户管理

# 更新规则
PUT /admin-api/emss-inspection/rule/update
{
  "id": 1,
  "ruleName": "更新后的规则名称",
  "priority": 2
}

# 删除规则
DELETE /admin-api/emss-inspection/rule/delete?id=1
```

#### 规则验证和测试
```bash
# 验证规则逻辑
POST /admin-api/emss-inspection/rule/validate
{
  "ruleId": 1,
  "testData": {
    "customer": {
      "name": "张三",
      "phone": "***********",
      "email": "<EMAIL>"
    }
  }
}

# 规则冲突检测
POST /admin-api/emss-inspection/rule/conflict-detection
{
  "ruleIds": [1, 2, 3],
  "scope": "SAME_CATEGORY"
}

# 规则性能测试
POST /admin-api/emss-inspection/rule/performance-test
{
  "ruleId": 1,
  "testDataSize": 1000,
  "concurrency": 10
}
```

### 规则池管理API

#### 规则池操作
```bash
# 创建规则池
POST /admin-api/emss-inspection/rule-pool/create
{
  "poolName": "客户管理稽查规则池",
  "category": "客户管理",
  "description": "客户管理相关的所有稽查规则",
  "ruleIds": [1, 2, 3, 4]
}

# 获取规则池列表
GET /admin-api/emss-inspection/rule-pool/page

# 添加规则到规则池
POST /admin-api/emss-inspection/rule-pool/{poolId}/add-rules
{
  "ruleIds": [5, 6, 7]
}

# 从规则池移除规则
POST /admin-api/emss-inspection/rule-pool/{poolId}/remove-rules
{
  "ruleIds": [2, 3]
}

# 激活规则池
POST /admin-api/emss-inspection/rule-pool/{poolId}/activate

# 停用规则池
POST /admin-api/emss-inspection/rule-pool/{poolId}/deactivate
```

### 可视化规则编排API

#### 流程设计
```bash
# 创建规则流程
POST /admin-api/emss-inspection/workflow/create
{
  "workflowName": "客户稽查流程",
  "category": "客户管理",
  "flowDefinition": {
    "nodes": [
      {
        "id": "start",
        "type": "START",
        "name": "开始"
      },
      {
        "id": "rule1",
        "type": "RULE",
        "name": "客户信息完整性检查",
        "ruleId": 1
      },
      {
        "id": "decision1",
        "type": "DECISION",
        "name": "是否通过检查",
        "condition": "rule1.result == 'PASS'"
      },
      {
        "id": "action1",
        "type": "ACTION",
        "name": "发送告警",
        "actionType": "ALERT"
      },
      {
        "id": "end",
        "type": "END",
        "name": "结束"
      }
    ],
    "edges": [
      {"from": "start", "to": "rule1"},
      {"from": "rule1", "to": "decision1"},
      {"from": "decision1", "to": "action1", "condition": "false"},
      {"from": "decision1", "to": "end", "condition": "true"},
      {"from": "action1", "to": "end"}
    ]
  }
}

# 获取流程列表
GET /admin-api/emss-inspection/workflow/page

# 执行流程
POST /admin-api/emss-inspection/workflow/{workflowId}/execute
{
  "inputData": {
    "customer": {
      "id": "CUST001",
      "name": "张三",
      "phone": "***********"
    }
  }
}
```

### 智能稽查执行API

#### 稽查任务管理
```bash
# 创建稽查任务
POST /admin-api/emss-inspection/task/create
{
  "taskName": "月度客户稽查",
  "taskType": "SCHEDULED",
  "rulePoolId": 1,
  "dataSource": {
    "type": "DATABASE",
    "connectionString": "***************************************",
    "query": "SELECT * FROM customers WHERE create_time >= '2024-01-01'"
  },
  "schedule": {
    "cronExpression": "0 0 2 1 * ?",
    "timezone": "Asia/Shanghai"
  },
  "notificationConfig": {
    "enabled": true,
    "recipients": ["<EMAIL>"],
    "channels": ["email", "sms"]
  }
}

# 手动执行稽查任务
POST /admin-api/emss-inspection/task/{taskId}/execute

# 获取稽查结果
GET /admin-api/emss-inspection/task/{taskId}/results?pageNo=1&pageSize=10

# 获取稽查统计
GET /admin-api/emss-inspection/task/{taskId}/statistics
```

#### AI分析和建议
```bash
# 生成稽查报告
POST /admin-api/emss-inspection/analysis/generate-report
{
  "taskId": 1,
  "reportType": "COMPREHENSIVE",
  "includeRecommendations": true,
  "aiModel": "deepseek"
}

# 获取改进建议
POST /admin-api/emss-inspection/analysis/improvement-suggestions
{
  "taskId": 1,
  "analysisScope": "RULE_EFFECTIVENESS",
  "timeRange": "30d"
}

# 趋势分析
GET /admin-api/emss-inspection/analysis/trend?taskId=1&period=monthly&months=6
```

## 开发指南

### 项目结构
```
datamind-server-emss-inspection/
├── src/main/java/
│   └── com/data/platform/datamind/server/emssinspection/
│       ├── controller/
│       │   ├── admin/
│       │   │   ├── PolicyController.java              # 政策文件控制器
│       │   │   ├── RuleController.java                # 规则管理控制器
│       │   │   ├── RulePoolController.java            # 规则池控制器
│       │   │   ├── WorkflowController.java            # 工作流控制器
│       │   │   └── InspectionTaskController.java      # 稽查任务控制器
│       │   └── web/
│       ├── service/
│       │   ├── PolicyProcessingService.java           # 政策处理服务
│       │   ├── RuleManagementService.java             # 规则管理服务
│       │   ├── WorkflowEngineService.java             # 工作流引擎服务
│       │   ├── InspectionExecutionService.java       # 稽查执行服务
│       │   └── AIAnalysisService.java                 # AI分析服务
│       ├── engine/
│       │   ├── RuleEngine.java                        # 规则引擎
│       │   ├── WorkflowEngine.java                    # 工作流引擎
│       │   └── InspectionEngine.java                  # 稽查引擎
│       ├── ai/
│       │   ├── PolicyParserAI.java                    # 政策解析AI
│       │   ├── RuleGeneratorAI.java                   # 规则生成AI
│       │   └── InspectionAnalyzerAI.java              # 稽查分析AI
│       └── dal/                                       # 数据访问层
├── src/main/resources/
│   ├── application.yaml
│   ├── rules/                                         # 规则模板
│   └── workflows/                                     # 工作流模板
└── pom.xml
```

### 核心服务实现

#### 政策处理服务
```java
@Service
@Slf4j
public class PolicyProcessingServiceImpl implements PolicyProcessingService {
    
    @Override
    public PolicyParseResult parsePolicy(Long policyId, PolicyParseOptions options) {
        log.info("开始解析政策文件: {}", policyId);
        
        try {
            // 1. 获取政策文件
            PolicyDocumentDO policy = policyMapper.selectById(policyId);
            
            // 2. 文档预处理
            String content = extractTextContent(policy.getFilePath());
            String cleanedContent = preprocessContent(content);
            
            // 3. AI解析
            PolicyParseRequest request = PolicyParseRequest.builder()
                .content(cleanedContent)
                .extractRules(options.isExtractRules())
                .extractElements(options.isExtractElements())
                .build();
            
            PolicyParseResult result = policyParserAI.parsePolicy(request);
            
            // 4. 保存解析结果
            savePolicyParseResult(policyId, result);
            
            // 5. 构建知识图谱 (可选)
            if (options.isBuildKnowledgeGraph()) {
                buildPolicyKnowledgeGraph(policyId, result);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("政策文件解析失败", e);
            throw new BusinessException(POLICY_PARSE_FAILED, e.getMessage());
        }
    }
}
```

#### 规则引擎服务
```java
@Service
@Slf4j
public class RuleEngineServiceImpl implements RuleEngineService {
    
    @Override
    public RuleExecutionResult executeRule(Long ruleId, Map<String, Object> inputData) {
        log.info("执行稽查规则: {}", ruleId);
        
        try {
            // 1. 获取规则定义
            InspectionRuleDO rule = ruleMapper.selectById(ruleId);
            
            // 2. 构建规则执行上下文
            RuleExecutionContext context = RuleExecutionContext.builder()
                .ruleId(ruleId)
                .inputData(inputData)
                .executionTime(LocalDateTime.now())
                .build();
            
            // 3. 执行规则条件检查
            boolean conditionsMet = evaluateConditions(rule.getConditions(), inputData);
            
            // 4. 执行规则动作
            List<ActionResult> actionResults = new ArrayList<>();
            if (conditionsMet) {
                actionResults = executeActions(rule.getActions(), context);
            }
            
            // 5. 构建执行结果
            RuleExecutionResult result = RuleExecutionResult.builder()
                .ruleId(ruleId)
                .success(true)
                .conditionsMet(conditionsMet)
                .actionResults(actionResults)
                .executionTime(context.getExecutionTime())
                .build();
            
            // 6. 记录执行历史
            recordRuleExecution(result);
            
            return result;
            
        } catch (Exception e) {
            log.error("规则执行失败", e);
            return RuleExecutionResult.builder()
                .ruleId(ruleId)
                .success(false)
                .errorMessage(e.getMessage())
                .build();
        }
    }
}
```

### 扩展开发

#### 添加新的规则类型
```java
@Component
public class CustomRuleType implements RuleType {
    
    @Override
    public String getRuleTypeName() {
        return "CUSTOM_RULE";
    }
    
    @Override
    public boolean evaluateCondition(RuleCondition condition, Map<String, Object> data) {
        // 实现自定义规则条件评估逻辑
        return performCustomEvaluation(condition, data);
    }
    
    @Override
    public ActionResult executeAction(RuleAction action, RuleExecutionContext context) {
        // 实现自定义规则动作执行逻辑
        return performCustomAction(action, context);
    }
}
```

#### 添加新的AI分析器
```java
@Component
public class CustomInspectionAnalyzer implements InspectionAnalyzer {
    
    @Override
    public String getAnalyzerName() {
        return "custom-analyzer";
    }
    
    @Override
    public AnalysisResult analyze(InspectionData data) {
        // 实现自定义分析逻辑
        return performCustomAnalysis(data);
    }
}
```

## 配置和优化

### 规则引擎优化
```yaml
emss:
  inspection:
    rule-engine:
      # 执行配置
      execution:
        timeout: 30000
        max-concurrent-rules: 100
        enable-caching: true
        cache-ttl: 3600
      
      # 性能配置
      performance:
        batch-size: 1000
        parallel-execution: true
        thread-pool-size: 20
```

### AI服务优化
```yaml
datamind:
  ai:
    # 连接池配置
    http-client:
      max-connections: 50
      connection-timeout: 5000
      read-timeout: 30000
    
    # 重试配置
    retry:
      max-attempts: 3
      backoff-delay: 1000
```

## 监控和运维

### 性能监控
```bash
# 规则执行统计
curl http://localhost:8085/actuator/metrics/emss.rule.execution.count

# 稽查任务统计
curl http://localhost:8085/actuator/metrics/emss.inspection.task.count

# AI分析调用统计
curl http://localhost:8085/actuator/metrics/emss.ai.analysis.count
```

### 故障排查

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 政策解析失败 | 文件格式不支持或AI服务不可用 | 检查文件格式和AI服务状态 |
| 规则执行超时 | 规则逻辑复杂或数据量过大 | 优化规则逻辑或增加超时时间 |
| 稽查任务失败 | 数据源连接问题 | 检查数据源配置和网络连通性 |
| 内存使用过高 | 大量规则并发执行 | 调整并发参数和内存配置 |
