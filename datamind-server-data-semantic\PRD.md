# DataMind Data-Semantic 数据语义服务 - 产品需求文档 (PRD)

```yaml
# === PROJECT METADATA ===
project_name: "DataMind Data-Semantic 语义逻辑原子与关系管理服务"
version: "v1.0"
created_date: "2025-01-04"
last_updated: "2025-01-04"
project_type: "microservice"
complexity_level: "complex"
estimated_duration: "8 weeks"
service_port: 8083
```

## 🎯 产品概览 (Product Overview)

### 核心价值主张
> 语义知识管理核心服务，通过RAGFlow技术实现知识向量化和智能检索，为NL2SQL提供语义理解和检索增强支持

### 目标用户画像
- **主要用户**: 数据科学家、知识工程师、业务分析师、AI工程师
- **使用场景**: 
  - 语义原子定义和管理
  - 业务知识向量化存储
  - 智能语义检索和匹配
  - 知识图谱构建和查询
  - NL2SQL语义增强支持
- **用户痛点**: 
  - 业务语义与技术实现脱节
  - 知识分散，缺乏统一管理
  - 语义检索效率低，准确性差
  - 知识更新困难，维护成本高

### 成功指标
- **北极星指标**: 语义检索准确率 >90%
- **关键结果**: 
  - 知识向量化覆盖率 >95%
  - 语义检索响应时间 <500ms
  - RAG增强效果提升 >15%
- **验证假设**: 
  - 结构化的语义管理能提升NL2SQL准确性
  - 向量化知识检索能显著改善查询理解

## 🔧 技术架构 (Technical Architecture)

### 技术栈选择
```json
{
  "rag_engine": {
    "core": "RAGFlow",
    "vector_store": ["Milvus", "Qdrant", "Redis"],
    "embedding_models": "多种向量化模型支持",
    "retrieval_strategy": "混合检索（向量+关键词+图谱）"
  },
  "framework": {
    "core": "Spring Boot 2.7.18",
    "data_access": "MyBatis Plus ********",
    "cache": "Redis 6.0",
    "async_processing": "Spring Async"
  },
  "knowledge_management": {
    "datasets": "六大专业知识数据集",
    "chunking_methods": ["knowledge-graph", "laws", "manual"],
    "sync_mechanism": "实时和批量同步"
  },
  "graph_processing": {
    "graph_database": "Neo4j (可选)",
    "relationship_modeling": "语义原子关系图谱",
    "graph_query": "Cypher查询支持"
  }
}
```

### 架构约束
- **性能要求**: 语义检索 <500ms, 向量化处理 <2秒, 支持10万+语义原子
- **准确性要求**: 语义匹配准确率 >90%, 知识检索相关性 >85%
- **可扩展性**: 支持新知识源接入、支持大规模向量存储
- **可用性**: 99.5%服务可用性，支持知识热更新

## 📦 功能需求矩阵 (Feature Requirements Matrix)

### MVP版本 (P0 - 核心功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| DS001 | 语义原子管理 | 作为知识工程师，我希望管理语义原子，以便标准化业务概念 | M | 4d | - |
| DS002 | RAGFlow集成 | 作为系统，我希望集成RAGFlow，以便提供向量化和检索能力 | L | 5d | - |
| DS003 | 知识向量化 | 作为系统，我希望将知识向量化，以便支持语义检索 | L | 4d | DS002 |
| DS004 | 语义检索服务 | 作为用户，我希望进行语义检索，以便找到相关知识 | M | 3d | DS003 |
| DS005 | 六大知识数据集 | 作为系统，我希望管理六大知识数据集，以便提供全面的知识支持 | L | 5d | DS002 |

### 增强版本 (P1 - 重要功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| DS006 | 知识图谱构建 | 作为数据科学家，我希望构建知识图谱，以便分析语义关系 | L | 6d | DS001 |
| DS007 | 语义关系管理 | 作为知识工程师，我希望管理语义关系，以便建立知识联系 | M | 3d | DS006 |
| DS008 | RAG检索增强 | 作为系统，我希望提供RAG增强服务，以便支持其他服务 | L | 4d | DS004 |
| DS009 | 知识同步机制 | 作为系统，我希望同步知识更新，以便保持数据一致性 | M | 3d | DS005 |

### 完整版本 (P2 - 增值功能)
| 功能ID | 功能名称 | 用户故事 | 复杂度 | 工期 | 依赖 |
|--------|----------|----------|---------|------|------|
| DS010 | 知识质量评估 | 作为知识管理员，我希望评估知识质量，以便改进知识管理 | M | 3d | DS005 |
| DS011 | 智能知识推荐 | 作为用户，我希望获得知识推荐，以便发现相关内容 | M | 4d | DS008 |
| DS012 | 知识版本管理 | 作为知识工程师，我希望管理知识版本，以便追踪变更历史 | M | 3d | DS001 |

**复杂度说明**: S(Simple, 1-2天) | M(Medium, 3-5天) | L(Large, 6-10天) | XL(Extra Large, >10天)

## 📋 功能详细规格 (Detailed Specifications)

### 语义原子管理 - DS001
```yaml
feature_id: "DS001"
feature_name: "语义原子管理"
priority: "P0"
complexity: "M"
estimated_effort: "4d"
dependencies: []

description: |
  提供语义原子的完整生命周期管理，包括定义、存储、查询、
  更新和删除，支持业务概念的标准化和统一管理。

technical_specs:
  atom_types: ["DIMENSION", "MEASURE", "FILTER", "CALCULATION"]
  data_types: ["STRING", "NUMBER", "DATE", "BOOLEAN"]
  storage: "MySQL + Redis缓存"
  validation: "语义原子定义验证"
  
  api_endpoints:
    - method: "POST"
      path: "/admin-api/data-semantic/atoms/create"
      description: "创建语义原子"
      request_body: |
        {
          "atomName": "string",
          "atomCode": "string",
          "atomType": "string",
          "description": "string",
          "businessDomain": "string",
          "dataType": "string",
          "expression": "string",
          "sqlTemplate": "string",
          "tags": ["string"],
          "metadata": "object"
        }
      response_body: |
        {
          "code": 0,
          "data": {
            "id": "long",
            "atomCode": "string",
            "status": "string"
          }
        }
    - method: "GET"
      path: "/admin-api/data-semantic/atoms/page"
      description: "分页查询语义原子"
    - method: "PUT"
      path: "/admin-api/data-semantic/atoms/update"
      description: "更新语义原子"
    - method: "DELETE"
      path: "/admin-api/data-semantic/atoms/delete"
      description: "删除语义原子"

business_logic:
  - step: "原子定义验证"
    description: "验证语义原子定义的完整性和正确性"
  - step: "编码唯一性检查"
    description: "确保原子编码在业务域内唯一"
  - step: "SQL模板验证"
    description: "验证SQL模板的语法正确性"
  - step: "关系依赖检查"
    description: "检查与其他原子的依赖关系"
  - step: "版本管理"
    description: "管理语义原子的版本变更"

acceptance_criteria:
  - criterion: "支持四种类型的语义原子管理"
    test_method: "功能测试"
  - criterion: "原子编码唯一性约束生效"
    test_method: "数据完整性测试"
  - criterion: "SQL模板验证功能正常"
    test_method: "验证测试"
  - criterion: "支持原子的CRUD操作"
    test_method: "接口测试"
  - criterion: "原子查询响应时间小于200ms"
    test_method: "性能测试"

implementation_hints:
  code_generation_prompt: |
    生成语义原子管理系统，要求：
    1. 使用Spring Boot + MyBatis Plus
    2. 实现完整的CRUD操作
    3. 支持四种原子类型管理
    4. 包含原子定义验证
    5. 实现编码唯一性约束
    6. 添加SQL模板验证
    7. 支持标签和元数据管理
    8. 包含缓存机制
    9. 包含完整的测试用例
  
  key_considerations:
    - "原子编码的命名规范和唯一性"
    - "SQL模板的安全性验证"
    - "原子关系的循环依赖检测"
    - "版本变更的影响分析"
    - "缓存更新的一致性保证"
```

### RAGFlow集成 - DS002
```yaml
feature_id: "DS002"
feature_name: "RAGFlow集成"
priority: "P0"
complexity: "L"
estimated_effort: "5d"
dependencies: []

description: |
  集成RAGFlow引擎，提供知识向量化、存储和检索能力，
  支持多种向量数据库和检索策略。

technical_specs:
  ragflow_integration: "RAGFlow API集成"
  vector_databases: ["Milvus", "Qdrant", "Redis"]
  embedding_models: "支持多种向量化模型"
  chunking_strategies: ["knowledge-graph", "laws", "manual"]
  
  api_endpoints:
    - method: "POST"
      path: "/admin-api/data-semantic/ragflow/datasets/create"
      description: "创建RAGFlow数据集"
      request_body: |
        {
          "name": "string",
          "description": "string",
          "chunkMethod": "string",
          "embeddingModel": "string",
          "vectorStore": "string"
        }
    - method: "POST"
      path: "/admin-api/data-semantic/ragflow/documents/upload"
      description: "上传文档到数据集"
    - method: "POST"
      path: "/admin-api/data-semantic/ragflow/search"
      description: "RAGFlow语义搜索"
    - method: "GET"
      path: "/admin-api/data-semantic/ragflow/datasets/list"
      description: "获取数据集列表"

business_logic:
  - step: "RAGFlow连接配置"
    description: "配置RAGFlow服务连接参数"
  - step: "数据集创建"
    description: "在RAGFlow中创建知识数据集"
  - step: "文档上传处理"
    description: "处理文档上传和向量化"
  - step: "检索服务封装"
    description: "封装RAGFlow检索接口"
  - step: "结果后处理"
    description: "处理和优化检索结果"

acceptance_criteria:
  - criterion: "成功连接RAGFlow服务"
    test_method: "连接测试"
  - criterion: "支持多种向量数据库配置"
    test_method: "配置测试"
  - criterion: "文档向量化功能正常"
    test_method: "功能测试"
  - criterion: "语义检索准确率达到85%以上"
    test_method: "准确率测试"
  - criterion: "检索响应时间小于500ms"
    test_method: "性能测试"

implementation_hints:
  code_generation_prompt: |
    生成RAGFlow集成系统，要求：
    1. 集成RAGFlow API
    2. 支持多种向量数据库
    3. 实现数据集管理
    4. 支持文档上传和处理
    5. 封装检索接口
    6. 包含错误处理和重试
    7. 添加性能监控
    8. 包含完整的测试用例
  
  key_considerations:
    - "RAGFlow服务的可用性监控"
    - "向量数据库的性能优化"
    - "文档处理的异步机制"
    - "检索结果的缓存策略"
    - "API调用的限流和重试"
```

## 📊 数据模型 (Data Models)

### 核心实体定义
```typescript
// 语义原子实体
interface SemanticAtom {
  id: number;
  atomName: string;
  atomCode: string;
  atomType: string; // DIMENSION, MEASURE, FILTER, CALCULATION
  description: string;
  businessDomain: string;
  dataType: string;
  expression: string;
  sqlTemplate: string;
  status: string; // ACTIVE, INACTIVE, DEPRECATED
  version: string;
  tags: string[]; // JSON格式
  metadata: object; // JSON格式
  creatorId: number;
  createTime: Date;
  updateTime: Date;
}

// 语义关系实体
interface SemanticRelation {
  id: number;
  sourceAtomId: number;
  targetAtomId: number;
  relationType: string; // DEPENDS_ON, DERIVES_FROM, CONFLICTS_WITH
  relationStrength: number;
  description: string;
  status: string;
  createTime: Date;
  updateTime: Date;
}

// RAGFlow数据集实体
interface RagflowDataset {
  id: number;
  datasetId: string; // RAGFlow中的数据集ID
  name: string;
  description: string;
  chunkMethod: string;
  embeddingModel: string;
  vectorStore: string;
  documentCount: number;
  chunkCount: number;
  status: string;
  createTime: Date;
  updateTime: Date;
}

// 知识文档实体
interface KnowledgeDocument {
  id: number;
  datasetId: number;
  documentId: string; // RAGFlow中的文档ID
  name: string;
  type: string;
  size: number;
  chunkCount: number;
  processingStatus: string;
  uploadTime: Date;
  processTime: Date;
}

// 语义检索记录
interface SemanticSearchRecord {
  id: number;
  userId: number;
  query: string;
  datasetIds: string[];
  topK: number;
  threshold: number;
  resultCount: number;
  responseTime: number;
  searchTime: Date;
}
```

### 六大知识数据集定义
```yaml
knowledge_datasets:
  semantic_atoms_knowledge:
    name: "语义原子知识库"
    description: "语义原子定义、属性、使用场景"
    chunk_method: "knowledge-graph"
    purpose: "NL2SQL语义理解和转换"

  semantic_atom_relations_knowledge:
    name: "语义原子关系知识库"
    description: "语义原子间的依赖、派生、冲突关系"
    chunk_method: "knowledge-graph"
    purpose: "语义关系推理和验证"

  metadata_knowledge:
    name: "元数据知识库"
    description: "数据表、字段、血缘关系等元数据信息"
    chunk_method: "knowledge-graph"
    purpose: "数据结构理解和查询优化"

  metadata_relations_knowledge:
    name: "元数据关系知识库"
    description: "表间关系、字段关系、血缘关系"
    chunk_method: "knowledge-graph"
    purpose: "数据血缘分析和关系发现"

  policy_documents_knowledge:
    name: "政策文件知识库"
    description: "政策文档、法规、标准等文件内容"
    chunk_method: "laws"
    purpose: "合规检查和政策理解"

  business_rules_knowledge:
    name: "业务规则知识库"
    description: "业务规则、计算逻辑、验证规则"
    chunk_method: "manual"
    purpose: "业务逻辑理解和规则应用"
```

### API设计规范
```yaml
# RESTful API 设计标准
api_base_url: "http://localhost:8083"
authentication: "Bearer Token (JWT)"
rate_limiting: "100 requests/minute per user"

# 统一响应格式
response_format:
  success: |
    {
      "code": 0,
      "data": {...},
      "msg": "操作成功"
    }
  error: |
    {
      "code": 500,
      "data": null,
      "msg": "操作失败"
    }

# 核心API端点
api_endpoints:
  semantic_atoms:
    - "GET /admin-api/data-semantic/atoms/page"
    - "POST /admin-api/data-semantic/atoms/create"
    - "PUT /admin-api/data-semantic/atoms/update"
    - "DELETE /admin-api/data-semantic/atoms/delete"
    - "GET /admin-api/data-semantic/atoms/{id}/relations"

  ragflow_integration:
    - "GET /admin-api/data-semantic/ragflow/datasets/list"
    - "POST /admin-api/data-semantic/ragflow/datasets/create"
    - "POST /admin-api/data-semantic/ragflow/documents/upload"
    - "POST /admin-api/data-semantic/ragflow/search"
    - "GET /admin-api/data-semantic/ragflow/datasets/{id}/status"

  knowledge_management:
    - "POST /admin-api/data-semantic/knowledge/sync"
    - "GET /admin-api/data-semantic/knowledge/datasets"
    - "POST /admin-api/data-semantic/knowledge/search"
    - "GET /admin-api/data-semantic/knowledge/statistics"

  semantic_services:
    - "POST /api/v1/data-semantic/search"
    - "POST /api/v1/data-semantic/enhance"
    - "GET /api/v1/data-semantic/atoms/suggest"
    - "POST /api/v1/data-semantic/relations/query"
```

## 🗓️ 实施路线图 (Implementation Roadmap)

### 迭代计划
```yaml
sprint_1:
  duration: "2 weeks"
  goal: "基础语义管理和RAGFlow集成"
  deliverables:
    - feature_id: "DS001"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "语义原子管理系统"
    - feature_id: "DS002"
      status: "必须完成"
      assignee: "AI团队"
      description: "RAGFlow集成框架"
    - database_setup: "完成"
      assignee: "DBA团队"
      description: "语义数据库表结构设计"

sprint_2:
  duration: "2 weeks"
  goal: "知识向量化和检索服务"
  deliverables:
    - feature_id: "DS003"
      status: "必须完成"
      assignee: "AI团队"
      description: "知识向量化处理"
    - feature_id: "DS004"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "语义检索服务"
    - feature_id: "DS005"
      status: "必须完成"
      assignee: "数据团队"
      description: "六大知识数据集构建"

sprint_3:
  duration: "2 weeks"
  goal: "知识图谱和关系管理"
  deliverables:
    - feature_id: "DS006"
      status: "必须完成"
      assignee: "数据团队"
      description: "知识图谱构建"
    - feature_id: "DS007"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "语义关系管理"
    - feature_id: "DS008"
      status: "必须完成"
      assignee: "AI团队"
      description: "RAG检索增强服务"

sprint_4:
  duration: "2 weeks"
  goal: "同步机制和增强功能"
  deliverables:
    - feature_id: "DS009"
      status: "必须完成"
      assignee: "后端开发团队"
      description: "知识同步机制"
    - feature_id: "DS010"
      status: "可选完成"
      assignee: "数据团队"
      description: "知识质量评估"
    - performance_optimization: "完成"
      assignee: "全体团队"
      description: "性能优化和监控"

# 里程碑检查点
milestones:
  basic_functionality:
    date: "2025-02-28"
    criteria: "完成基础语义管理和RAGFlow集成"
    deliverables: ["语义原子管理", "RAGFlow集成", "知识向量化", "语义检索"]

  advanced_features:
    date: "2025-03-14"
    criteria: "完成知识图谱和关系管理"
    deliverables: ["知识数据集", "知识图谱", "语义关系", "RAG增强"]

  production_ready:
    date: "2025-03-28"
    criteria: "完成所有功能，通过性能测试"
    deliverables: ["同步机制", "质量评估", "智能推荐", "版本管理"]
```

### 质量保证
- **准确率要求**: 语义检索准确率 >90%, 知识匹配相关性 >85%
- **性能基准**: 语义检索 <500ms, 向量化处理 <2秒
- **可用性**: RAGFlow服务可用性 >99%, 知识同步成功率 >95%
- **扩展性**: 支持10万+语义原子, 支持TB级知识存储

## 🤖 AI协作配置 (AI Collaboration Config)

### 代码生成上下文
```yaml
project_context:
  tech_stack: "Spring Boot + RAGFlow + Milvus/Qdrant + Redis"
  coding_style: "阿里巴巴Java开发规范"
  project_structure: |
    datamind-server-data-semantic/
    ├── src/main/java/com/data/platform/datamind/server/semantic/
    │   ├── controller/     # REST控制器
    │   ├── service/        # 业务逻辑层
    │   ├── dal/           # 数据访问层
    │   ├── ragflow/       # RAGFlow集成
    │   ├── knowledge/     # 知识管理
    │   └── graph/         # 图谱操作

code_generation_templates:
  semantic_prompt: |
    生成语义原子管理系统，要求：
    1. 使用Spring Boot + MyBatis Plus
    2. 实现语义原子CRUD操作
    3. 支持原子类型和关系管理
    4. 包含验证和约束检查
    5. 添加缓存机制
    6. 包含完整测试用例

  ragflow_prompt: |
    生成RAGFlow集成系统，要求：
    1. 集成RAGFlow API
    2. 实现知识向量化
    3. 支持多种向量数据库
    4. 包含检索和增强功能
    5. 添加性能监控
    6. 包含异常处理
```

## 💡 质量保证清单

- [x] 所有P0功能都有明确的验收标准
- [x] 技术规格可以直接用于代码生成
- [x] API设计符合RESTful规范
- [x] 数据模型定义完整
- [x] 实施计划具有可执行性
- [x] AI协作配置完整可用

## 📚 附录

### 相关文档链接
- [服务详细指南](./GUIDE.md)
- [任务清单](./TASK.md)
- [数据库设计](../design/data-semantic/database.sql)
- [API接口文档](http://localhost:8083/doc.html)

### 风险评估
- **技术风险**: RAGFlow服务稳定性，向量数据库性能
- **数据风险**: 知识质量和一致性，同步机制可靠性
- **成本风险**: 向量存储成本，AI服务调用费用

---

**注意**: 本PRD文档专门针对DataMind Data-Semantic语义服务设计，作为知识管理的核心，需要确保语义准确性和检索效率。
